<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/header.jsp"%>
<html>
<head>
<title>自营厅可变成本录入</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet"
	type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet"
	type="text/css" />
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<div class="container">
		<div class="row" style="padding: 8px; height: auto">
			<div class="row">
				<form class="form-horizontal" id="ff">
					<fieldset class="span24">
						<legend>成本信息</legend>
						<div class="row">
							<div class="control-group span7">
								<label class="control-label">自营厅：</label>
								<div class="controls">
									<input id="nodeId" style="width: 150px;" data-options="required:true,editable:false"/>
								</div>
								<div id="sp">
									<div style="margin: 2px;">
										<span> <input id="key" class="easyui-validatebox"
											style="width: 90px;" />
										</span> <span> <a id="treeQueryBtn" href="#"
											class="easyui-linkbutton"
											data-options="iconCls:'icon-search'"></a>
										</span>
									</div>
									<div style="margin: 2px;">
										<ul id="tt"></ul>
									</div>
								</div>
							</div>
							<div class="control-group span7">
								<label class="control-label">月份：</label>
								<div>
									<select id="year" class="easyui-combobox" data-options="required:true,editable:false" name="YEAR_STR" style="width: 60px;">
										<option value="2010">2010</option>
										<option value="2011">2011</option>
										<option value="2012">2012</option>
										<option value="2013">2013</option>
										<option value="2014">2014</option>
										<option value="2015">2015</option>
										<option value="2016">2016</option>
										<option value="2017">2017</option>
										<option value="2018">2018</option>
										<option value="2019">2019</option>
										<option value="2020">2020</option>
										<option value="2021">2021</option>
										<option value="2022">2022</option>
										<option value="2023">2023</option>
										<option value="2024">2024</option>
										<option value="2025">2025</option>
										<option value="2026">2026</option>
										<option value="2027">2027</option>
										<option value="2028">2028</option>
										<option value="2029">2029</option>
										<option value="2030">2030</option>
									</select>&nbsp;年 <select id="month" class="easyui-combobox" data-options="required:true,editable:false" name="MONTH_STR" style="width: 55px;">
										<option value="1">01</option>
										<option value="2">02</option>
										<option value="3">03</option>
										<option value="4">04</option>
										<option value="5">05</option>
										<option value="6">06</option>
										<option value="7">07</option>
										<option value="8">08</option>
										<option value="9">09</option>
										<option value="10">10</option>
										<option value="11">11</option>
										<option value="12">12</option>
									</select>&nbsp;月
								</div>
							</div>
							<div class="control-group span7">
								<label class="control-label">水&nbsp;费：</label>
								<div class="controls">
									<input type="text" class="easyui-validatebox"
										data-options="panelHeight:'200',valueField:'id',textField:'text',required:true"
										style="width: 148px;" id="waterFee" name="WATER_FEE" />
								</div>
							</div>
						</div>

						<div class="row">
							<div class="control-group span7">
								<label class="control-label">电费：</label>
								<div class="controls">
									<input type="text" class="easyui-validatebox"
										data-options="panelHeight:'200',valueField:'id',textField:'text',required:true"
										id="powerFee" name="POWER_FEE" style="width: 148px"></input>
								</div>
							</div>
							<div class="control-group span7">
								<label class="control-label">取暖费：</label>
								<div>
									<input type="text" class="easyui-validatebox"
										data-options="panelHeight:'200',valueField:'id',textField:'text',required:true"
										id="heatFee" name="HEAT_FEE" style="width: 148px"></input>
								</div>
							</div>
							<div class="control-group span7">
								<label class="control-label">办公用品及耗材：</label>
								<div class="controls">
									<input type="text" class="easyui-validatebox"
										data-options="panelHeight:'200',valueField:'id',textField:'text',required:true"
										id="supplyFee" name="SUPPLY_FEE" style="width: 148px"></input>
								</div>
							</div>
						</div>

						<div class="row">
							
							<div class="control-group span7">
								<label class="control-label">人工成本及劳务费总额：</label>
								<div class="controls">
									<input type="text" class="easyui-validatebox"
										data-options="panelHeight:'200',valueField:'id',textField:'text',required:true"
										id="laborCost" name="LABOR_COST" style="width: 148px"></input>
								</div>
							</div>
							<div class="control-group span7">
								<label class="control-label">其他日常费用：</label>
								<div >
									<input type="text" class="easyui-validatebox"
										data-options="panelHeight:'200',valueField:'id',textField:'text',required:true"
										id="otherFee" name="OTHER_FEE" style="width: 148px"></input>
								</div>
							</div>
							<div class="control-group span7">
								<div class="control" align="center">
									<a id="saveBtn" href="javascript:void(0)" class="easyui-linkbutton"
										data-options="iconCls:'icon-save'">保存</a>
								</div>
							</div>
						</div>
					</fieldset>
				</form>
			</div>
		</div>
		<!-- 数据显示开始 -->
		<div class="row span24">
			<fieldset class="span24">
				<legend>成本列表</legend>
				<table id="monthCostTable" style="height: 250px" editable="false"
					data-options="rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
					<thead>
						<tr>
							<th data-options="checkbox:false"></th>
							<th
								data-options="field:'recId',align:'center',exportable:false,width:75,hidden:true">记录号</th>
							<th
								data-options="field:'nodeId',align:'center',exportable:false,width:75,hidden:false">渠道编号</th>
							<th
								data-options="field:'monthStr',align:'center',exportable:false,width:90,hidden:false">录入成本月份</th>
							<th
								data-options="field:'createDate',align:'center',exportable:false,width:10,hidden:true">创建时间</th>
							<th
								data-options="field:'doneDate',align:'center',exportable:false,width:130,hidden:false">操作时间</th>
							<th
								data-options="field:'waterFee',align:'center',exportable:false,width:100,hidden:false">水费</th>
							<th
								data-options="field:'powerFee',align:'center',exportable:false,width:100,hidden:false">电费</th>
							<th
								data-options="field:'heatFee',align:'center',exportable:false,width:100,hidden:false">取暖费</th>
							<th
								data-options="field:'supplyFee',align:'center',exportable:false,width:100,hidden:false">办公用品及耗材</th>
							<th
								data-options="field:'laborCost',align:'center',exportable:false,width:100,hidden:false">人工成本及劳务费总额</th>
							<th
								data-options="field:'otherFee',align:'center',exportable:false,width:100,hidden:false">其他日常费用</th>
							<th
								data-options="field:'opId',align:'center',exportable:false,width:80,hidden:false">操作员</th>
							<th
								data-options="field:'orgId',align:'center',exportable:false,width:100,hidden:false">操作员组织</th>
							<th
								data-options="field:'RecStutas',align:'center',exportable:false,width:80,hidden:true">记录有效性</th>
						</tr>
					</thead>
				</table>
			</fieldset>
		</div>
		<!-- 数据显示结束 -->
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript"
	src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js"
	data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/addChannelMonthCost',
			function(ChannelNodeMonthCost) {
				new ChannelNodeMonthCost();
			});
</script>
</html>