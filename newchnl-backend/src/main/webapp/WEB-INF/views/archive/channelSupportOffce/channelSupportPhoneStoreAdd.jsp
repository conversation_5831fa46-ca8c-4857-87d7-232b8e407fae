<%--
 * 
 * $Id: channelSupportPhoneStoreAdd.jsp,v 1.18 2014/11/11 06:19:15 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>支撑手机卖场信息添加</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
		.form-horizontal .controls {
			height: 30px;
		}
		.form-horizontal .control-label {
			height: 30px;
			width: 100px;
			line-height: 20px;
		}
		
		input[type="text"], input[type="password"], input[type="email"]{
			width:100px;
		}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
    <div data-options="region:'west',split:true"  border="false" title="组织树" style="width:200px; padding: 10px;" id="actionTree">
	</div>
     
      <div region="center" title="" border="false" style="padding: 10px;">
		<form class="form-horizontal" id="ff" method="post">
		 <a href="#" onclick="javascript:history.go(-1);">返回上一层</a>>>支撑手机卖场信息添加
		<hr />
				<input name="agentId" id="agentId" type="hidden">
				<input name="nodeId" id="nodeId" type="hidden">
				<div class="row">
					<div class="control-group span6">
						<label class="control-label">网点名称：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" id="nodeDesignation" name="nodeDesignation" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span10">
						<label class="control-label">合作方：<s>*</s></label>
						 <div class="controls">
							<!-- <input type="text" class="easyui-validatebox" data-options="required:false" name="channelEntityName" id="channelEntityName" readonly="readonly"></input> -->
							<input name="channelEntityName" id="channelEntityName" style="width: 150px;" />
						</div>
						<div id="sp">
								<div style="margin: 2px;">
									<span> <input id="key" class="easyui-validatebox" style="width: 90px;" />
									</span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								<div style="margin: 2px;">
									<ul id="tt"></ul>
								</div>
							</div>
					</div>
					</div>
					<div class="row">
					  <div class="control-group span6">
						<label class="control-label">月目标：<s>*</s></label>
						  <div class="controls">
							<input type="text" class="easyui-numberbox" data-options="required:false,validType:'monthTarget'" id="monthTarget" name="monthTarget"></input>
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">考核得分：<s>*</s></label>
						  <div class="controls">
							<input type="text" class="easyui-numberbox" data-options="required:false,validType:'score'" id="score" name="score"></input>
						</div>
					</div>
				</div>
				<div class="row">
					
					<div class="control-group span6">
						<label class="control-label">组别：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" id="team" name="team"></input>
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">支撑人员数：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" data-options="required:false,validType:'supportNum'" id="supportNum" name="supportNum"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span10">
						<label class="control-label">支撑年月：<s>*</s></label>
						<div class="controls">
							<input  class="easyui-combobox"  
						   data-options="panelHeight:'200',
										required:false,
										editable:false,
										valueField:'id',textField:'text'" name="supportDate"  id="supportDate"></input>
						</div>
					</div>
				</div>
				<div class="row">
			     <div class="offset14" id="add">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="saveBtn">保存</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-undo'" id="resetBtn">重置</a>
			</div>
		</div>	
			</form>
		</div>
		
		<!-- 弹出层 -->
	   <div id="win" class="easyui-window" title="合作方信息查询" style="width:400px;height:400px"   
      	data-options="iconCls:'icon-save',modal:true,closed:true">
      	<div class="container span11">
			<form class="form-horizontal">
				<div class="row">
				<div class="control-group span8">
						<label class="control-label">合作方名称：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" id="channel_entityName"></input>
						</div>
					</div>
					
					<div class="span10 offset4">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">搜索</a>
					</div>
				</div>
			</form>
		

		<div id="tb" style="padding:8px; height:auto">
			<div style="margin-bottom:8px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-ok',plain:true" id="okBtn">添加</a>
			</div>
		</div>
		<div class="row">
			<table id="channelEntityBasicInfoTable" title="合作方" class="span5"
				data-options="rownumbers:true,singleSelect:true">
			    <thead>
			        <tr>
			        	<th data-options="field:'ck',checkbox:true"></th>
		    			<th data-options="field:'channelEntityId'" hidden="true">合作方id</th>
		    			<th data-options="field:'channelEntityName',width:'100',align:'center'">合作方名称</th>
			        </tr>
			    </thead>
			</table>
		</div>
	</div>   
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- 年月日期控件 -->
<script type="text/javascript" src="${ctx}/js/module/archive/channelSupportOffce/yearMonthTime.js"></script>
<!-- 扩展验证 -->
<script type="text/javascript" src="${ctx }/js/common/jquery.validatebox.extends.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use('module/archive/channelSupportOffce/channelSupportPhoneStoreeAdd',function(ChannelSupportPhoneStoreeAdd){
	new ChannelSupportPhoneStoreeAdd();
});
</script>
</html>