<%--
 * 
 * $Id: channelSupportPhoneStoreController.jsp,v 1.20 2015/02/17 17:04:27 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>支撑手机卖场信息管理</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
<div class="container span24">
		<div class="row" style="padding:8px; height:auto">
			<form class="form-horizontal">
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">网点名称：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" id="nodeDesignation"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">支撑日期：</label>
						<div class="controls">
							<input  class="easyui-combobox"  
						   data-options="panelHeight:'200',
										required:false,
										editable:false,
										valueField:'id',textField:'text'"  id="supportDate"></input>
						</div>
					</div>
				</div>
				<div class="row">
					
					<div class="span8 offset13">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">搜索</a>
					</div>
				</div>
			</form>
		</div>
		
		<div id="tb" style="padding:8px; height:auto">
			<div style="margin-bottom:8px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true" id="xlsAddBtn">导入</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true" id="xlsBtn">导出</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true" id="editBtn">修改</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="addBtn">添加</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="deletesBtn">删除</a>
			</div>
		</div>
		<div class="row">
			<table id="channelSupportPhoneStoreTable" title="支撑手机卖场信息" class="span24" style="height:500px;width:1010px"
				data-options="pageSize:10,height:435,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
			    <thead>
			        <tr>
			        	<th data-options="field:'ck',checkbox:true"></th>
		    			<th data-options="field:'angetName',width:'120',align:'center'">合作方</th>
		    			<th data-options="field:'nodeDesignation',width:'120',align:'center'">支撑网点</th>
		    			<th data-options="field:'supportDate',exportable:false,width:'120',align:'center'">支撑日期</th>
		    			<th data-options="field:'supportNum',width:'100',align:'center'">支撑人员数</th>
		    			<th data-options="field:'monthTarget',width:'100',align:'center'">月目标</th>
		    			<th data-options="field:'score',width:'100',align:'center'">考核得分</th>
		    			<th data-options="field:'team',width:'100',align:'center'">组别</th>
			        </tr>
			    </thead>
			</table>
		</div>
		<!-- xls导入弹出层 -->
	<div id="channelSupportPhoneStroeXlsWindow" class="easyui-window" title="支撑手机卖场信息批量导入" style="width:400px;" 
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div class="container" >
		<form class="form-horizontal">
			<div class="row" style="padding:8px; height:auto">
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">模版示例：</label>
							<a href="#" id="supportPhoneStroe">支撑手机卖场导入模板</a>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">选择导入日期：</label>
							<div class="controls">
								<input  class="easyui-combobox"  data-options="valueField:'id',textField:'text'"  id="xls_date"></input>
							</div>
						</div>
					</div>
					<div class="row">
					<div class="control-group span8">
						   <label class="control-label">批量导入：</label>
						  <div class="controls">
						   		<input type="file"  data-options="required:true"  id="fileinput" name="files"  />
						   </div>
						</div>
					</div>
					<div class="row">
						<div class="offset4">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="saveBtn">保存</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleBtn">取消</a>
						</div>
					</div>
			</div>
			</form>
		</div>
	</div>
		
		<!-- 修改弹出层 -->
	<div id="channelSupportPhoneStoreUpdateWindow" class="easyui-window" title="支撑手机卖场信息编辑" 
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div class="container" >
			<div class="row" style="padding:8px; height:auto">
				<form class="form-horizontal">
					<input type="hidden" id="done_code"></input>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">合作方：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="anget_name" disabled="disabled"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">网点名称：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="node_designation" disabled="disabled"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">月目标：</label>
							<div class="controls">
								<input type="text" class="easyui-numberbox" data-options="required:true" id="month_target"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">支撑人员数：</label>
							<div class="controls">
								<input type="text" class="easyui-numberbox" data-options="required:true" id="support_num"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">考核得分：</label>
							<div class="controls">
								<input type="text" class="easyui-numberbox" data-options="required:true" id="score"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">组别：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="team"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
						<label class="control-label">支撑日期：</label>
						<div class="controls">
							 <input  class="easyui-combobox"  data-options="valueField:'id',textField:'text'"  id="support_date"></input>
						</div>
					</div>
					</div>
					<div class="row">
						<div class="offset4">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-pencil" id="updateBtn">修改</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleUpdateBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
		
</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<!-- 年月日期控件 -->
<script type="text/javascript" src="${ctx}/js/module/archive/channelSupportOffce/yearMonthTime.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/channelSupportOffce/channelSupportPhoneStore',function(ChannelSupportPhoneStore){
		new ChannelSupportPhoneStore();
	});
</script>
</html>