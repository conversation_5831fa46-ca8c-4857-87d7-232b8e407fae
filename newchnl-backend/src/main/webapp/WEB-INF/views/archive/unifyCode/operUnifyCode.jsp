<%--
 * 
 * $Id: operUnifyCode.jsp,v 1.1 2014/10/24 03:13:21 linfeng Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>终端网点信息管理</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	 
</head>
<body>
	<div class="container span24">
		<div class="row">
			<form class="form-horizontal">
				<div class="row">
					<div class="span6 control-group">
						<label class="control-label">网点名称：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:false" id="channelEntityName" />
						</div>
					</div>
					<div class="span6 control-group">
						<label class="control-label">网点类型：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="nodeKind">
								<option value="">全部</option>
								<option value="1">直营营业厅</option>
								<option value="2">加盟营业厅</option>
								<option value="3">加盟社会店</option>
								<option value="4">手机专卖店</option>
								<option value="5">授权代理店</option>
								<option value="6">手机卖场</option>
								<option value="7">自助终端</option>
								<option value="8">校园直销队</option>
								<option value="9">家庭业务代理店</option>
								<option value="10">其它网点</option>
							</select>
						</div>
					</div>
				</div>
			</form>

			<div class="row  offset12" >
				<a href="javascript:void(0)" class="easyui-linkbutton" 
					data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
			</div>
		</div>

		<!-- operate 开始 -->
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="del">删除全网编码</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="add">新增全网编码</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true" id="deladd">删除后重生成</a>
			</div>
		</div>
		<!-- operate 结束 -->

		<!-- 数据显示开始 -->
		<div class="row span24">
			<table id = "unifyCode" class="easyui-datagrid" style="height:400px"   
                     data-options="singleSelect:false">   
                <thead>   
			        <tr>   
			           	<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'channelEntityId',width:'110'">网点ID</th>
						<th data-options="field:'channelEntityName',width:'110',align:'center'">网点名称</th>
						<!--<th data-options="field:'nodeKind',width:'110',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('10033',value);}">网点类型</th>  -->
						<th data-options="field:'parentEntity',width:'110',align:'center'">父代理商Id</th>
						<th data-options="field:'unifyCode',width:'110',align:'center'">全网编码</th>
						<th data-options="field:'channelEntitySerial',width:'110',align:'center'">是否正确</th>
						<th data-options="field:'dateType',width:'190',align:'center'">备注</th>
			        </tr>   
    			</thead>   
            </table> 
		</div>
		<!-- 数据显示结束 -->
	</div>
	<!-- 容器结束 -->





</body>

<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- 年月日期控件 -->
<script type="text/javascript" src="${ctx}/js/module/archive/channelSupportOffce/yearMonthTime.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use('module/archive/unifyCode/operUnifyCode',function(OperUnifyCode){
	new OperUnifyCode();
});
</script>
</html>
	