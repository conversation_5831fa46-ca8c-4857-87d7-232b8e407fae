<%--
  Created by IntelliJ IDEA.
  User: dull
  Date: 2021/8/10
  Time: 14:10
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@include file="/common/header.jsp" %>
<html>
    <head>
        <title>承包商资质名单管理</title>
        <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet"
              type="text/css"/>
        <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet"
              type="text/css"/>
        <link rel="stylesheet" type="text/css"
              href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
        <link rel="stylesheet" type="text/css"
              href="${ctx}/assets/easyui/themes/icon.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    </head>
    <body>
        <!-- 容器开始 -->

        <div class="container span24">

            <div class="row">
                <label class="control-label">合作方：</label>
                <div class="controls" style="display: inline">
                    <input id="cc"/>
                </div>
                <div id="sp" style="display: inline">
                    <div style="margin: 2px;">
                        <span> <input id="key" class="easyui-validatebox" style="width: 90px;"/>
                        </span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton"
                                          data-options="iconCls:'icon-search'"></a></span>
                    </div>
                    <div style="margin: 2px;">
                        <ul id="tt"></ul>
                    </div>
                </div>
                <div style="display: inline">
                    <a href="javascript:void(0)" class="easyui-linkbutton"
                       data-options="iconCls:'icon-add'" id="addBtn">新增</a></div>
            </div>

            <div class="row">
                <div class="row">
                    <div class="span6 control-group">
                        <label class="control-label">合作方简称：</label>
                        <div class="controls" style="display: inline">
                            <input type="text" class="easyui-validatebox"
                                   data-options="required:false" id="channelEntityName"/>
                        </div>
                        <div style="display: inline">
                            <a href="javascript:void(0)" class="easyui-linkbutton"
                               data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>

                            <a href="javascript:void(0)" class="easyui-linkbutton"
                               data-options="iconCls:'icon-remove'" id="deleteBtn">删除</a>
                        </div>


                    </div>
                </div>

            </div>

            <!-- operate 开始 -->
            <div class="row">
                <div class="control-group span24">
                    <div>
                        <label>承包商资质名单关系</label>
                        <label>模板下载：
                            <a href="#" id="contractorList">合作方简称与承包商资质对应关系模板.xls</a>
                        </label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="control-group span24">
                    <label class="control-label">导入文件：</label>
                    <input type="file" data-options="required:true" style="width: 340px" id="fileinput" name="file"/>
                    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel'"
                       id="import">导入</a>
                </div>
            </div>
            <!-- operate 结束 -->

            <!-- 数据显示开始 -->
            <div class="row span14">
                <table id="contractorInfo" title="查询结果"
                       data-options="rownumbers:true,singleSelect:false"
                       style="height: 400px">
                    <thead>
                        <tr>
                            <th data-options="field:'ck',checkbox:true"></th>
                            <%--                            <th data-options="field:'contractorId',width:'100',align:'center'">--%>
                            <%--                                合作方id--%>
                            <%--                            </th>--%>
                            <th data-options="field:'channelEntityName',width:'230',align:'center'">
                                合作方简称
                            </th>
                            <th data-options="field:'codeName',width:'230',align:'center'">
                                是否有承包商资质
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>
            <!-- 数据显示结束 -->
            <!-- 对承包商进行操作开始 -->
        </div>
        <!-- 容器结束 -->


    </body>
    <script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
    <script type="text/javascript"
            src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
    <!-- xls导出引入 -->
    <script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
    <script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
    <script type="text/javascript" src="${ctx}/assets/bui/seed-min.js"
            data-debug="true"></script>
    <script type="text/javascript" src="${ctx}/js/common/config.js"></script>
    <script>
        BUI.use('module/archive/contractorManager/contractorQualificationListManager', function (ContractorQualificationListManager) {
            new ContractorQualificationListManager();
        });
    </script>
</html>
