<%--
  Created by IntelliJ IDEA.
  User: dull
  Date: 2021/8/10
  Time: 14:11
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/common/header.jsp" %>
<html>
    <head>
        <title>微格对应关系绑定</title>
        <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
        <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    </head>
    <body>


        <div class="span28">
            <div class="control-group span12" align="center"><!-- 承包商资质展示表 -->
                <label>承包商简称：</label>
                <input type="text" class="easyui-validatebox"
                       data-options="required:false" id="channelEntityName"
                       style="width:80px"/>
                <a href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-search'" id="queryBtn1">搜索</a>


                <table id="contractorInfo" style="height:300px"
                       data-options="rownumbers:true,singleSelect:false,fitColumns:false,striped:true,loadMsg:'加载中...'">
                    <thead>
                        <tr>
                            <th data-options="field:'ck',checkbox:true"></th>
                            <th data-options="field:'channelEntityId',align:'center',width:230,hidden:true">
                                合作方ID
                            </th>
                            <th data-options="field:'channelEntityName',align:'center',width:230">
                                合作方简称
                            </th>
                            <th data-options="field:'codeName' ,align:'center',width:200">
                                是否有承包商资质
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>

            <div class="control-group span12"><!-- 微格信息表 -->

                <div class="control-group">
                    <label class="control-label">承包开始时间</label>
                        <select  class="easyui-datebox" id="startTime"> </select>
                    <label class="control-label">承包结束时间</label>
                        <select class="easyui-datebox" id="endTime"> </select>
                </div>


                <table class="easyui-datagrid" id="smallGridInfo" style="height:300px"
                       data-options="rownumbers:true,singleSelect:false,fitColumns:false,striped:true,loadMsg:'加载中...'">
                    <thead>
                        <tr>
                            <th data-options="checkbox:true"></th>
                            <th data-options="field:'smallGridId',align:'center',width:90,hidden:true">
                                微格ID
                            </th>
                            <th data-options="field:'smallGridName',align:'center',width:90,hidden:false">
                                微格名称
                            </th>
                            <th data-options="field:'gridName',align:'center',width:120,hidden:false">
                                微格所属网格
                            </th>
                            <th data-options="field:'countyName',align:'center',width:120,hidden:false">
                                微格所属分公司
                            </th>
                            <th data-options="field:'contractType',align:'center',width:100,formatter:function(value){
                                if(value=='SOCIAL_CHANNEL'){
                                return'社会渠道';
                                }else if(value=='HAVE_OWN'){
                                return'自有';
                                }
                                }">微格属性</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>


        <div class=span24>
            <label>承包商资质与微格信息关系列表</label>
            <div class="offset8">
                <a id="addBtn" href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-ok'">绑定</a>
                <a id="queryBtn2" href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-search'">查询</a>
                <a id="updateBtn" href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-edit'">修改</a>
                <a id="deleteBtn" href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-remove'">删除</a>
            </div>

            <div class="row">
                <div id="edit" class="easyui-dialog" style="width:400px;height:320px;padding:10px 20px"
                     closed="true" buttons="#dlg-buttons">
                        <table>
                            <tr hidden="true">
                                <th width="100px">
                                    <label>承包商id</label>
                                </th>
                                <th width="200px">
                                    <input id="contractorId">
                                </th>
                            </tr>
                            <tr>
                                <th width="100px">
                                    <label>微格名称</label>
                                </th>
                                <th width="200px">
                                    <input id="smallGridName" class="easyui-textbox" required="true">
                                </th>
                            </tr>
                            <tr>
                                <th width="100px">
                                    <label>所属分公司名称</label>
                                </th>
                                <th width="200px">
                                    <input id="countyName" class="easyui-textbox" required="true">
                                </th>
                            </tr>
                            <tr>
                                <th width="100px">
                                    <label>所属网格名称</label>
                                </th>
                                <th width="200px">
                                    <input id="gridName" class="text" required="true">
                                </th>
                            </tr>
                            <tr>
                                <th width="100px">
                                    <label>微格属性</label>
                                </th>
                                <th width="200px">
                                    <select required="true" id="contractType">
                                        <option value="SOCIAL_CHANNEL">社会渠道</option>
                                        <option value="HAVE_OWN">自有</option>
                                    </select>
                                </th>
                            </tr>
                            <tr>
                                <th width="100px">
                                    <label class="control-label">承包开始时间</label>
                                </th>
                                <th width="200px">
                                    <select  class="easyui-datebox" id="startTime1"> </select>
                                </th>
                            </tr>
                            <tr>
                                <th width="100px">
                                    <label class="control-label">承包结束时间</label>
                                </th>
                                <th width="200px">
                                    <select  class="easyui-datebox" id="endTime1"> </select>
                                </th>
                            </tr>
                        </table>
                </div>
                <div id="dlg-buttons">
                    <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" style="width:90px"
                       id="save">保存</a>
                    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" style="width:90px"
                       id="close">取消</a>
                </div>

                <div class="control-group"><!-- 承包商资质与微格信息关系列表 -->
                    <table id="contractorSmallGridTable" style="height:300px"
                           data-options="rownumbers:true,singleSelect:false,fitColumns:false,striped:true,loadMsg:'加载中...'">
                        <thead>
                            <tr>
                                <th data-options="checkbox:true"></th>
                                <th data-options="field:'smallGridId',hidden:true">微格ID</th>
                                <th data-options="field:'channelEntityName',align:'center',width:120,hidden:false">
                                    合作方简称
                                </th>
<%--                                <th data-options="field:'codeName' ,align:'center',width:120">是否有承包商资质</th>--%>
                                <th data-options="field:'smallGridName',align:'center',width:100">微格名称</th>
                                <th data-options="field:'gridName',align:'center',width:130">微格所属网格</th>
                                <th data-options="field:'countyName',align:'center',width:130">微格所属分公司</th>
                                <th data-options="field:'contractType',align:'center',width:100,formatter:function(value){
                                if(value=='SOCIAL_CHANNEL'){
                                return'社会渠道';
                                }else if(value=='HAVE_OWN'){
                                return'自有';}
                                }">微格属性</th>
                                <th data-options="field:'startTime',align:'center',width:130,formatter:function date(value) {var date = new Date(value);var year = date.getFullYear().toString();var month = (date.getMonth() + 1);var day = date.getDate().toString();
                                if (month < 10) {month = '0' + month;}
                                if (day < 10) {day = '0' + day;}
                                return year + '-' + month + '-' + day;}">承包开始时间
                                </th>
                                <th data-options="field:'endTime',align:'center',width:130,formatter:function date(value) {var date = new Date(value);var year = date.getFullYear().toString();var month = (date.getMonth() + 1);var day = date.getDate().toString();
                                if (month < 10) {month = '0' + month;}
                                if (day < 10) {day = '0' + day;}
                                return year + '-' + month + '-' + day;}">承包结束时间
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>

    </body>
    <script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
    <script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
    <script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
    <script type="text/javascript" src="${ctx}/js/common/config.js"></script>
    <script>
        BUI.use('module/archive/contractorManager/smallGridBinding', function (smallGridBinding) {
            new smallGridBinding();
        });
    </script>
</html>
