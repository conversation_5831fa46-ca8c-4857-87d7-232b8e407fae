<%--
  Created by IntelliJ IDEA.
  User: dull
  Date: 2021/8/10
  Time: 14:11
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
    <head>
        <title>微格对应关系查询</title>
        <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
        <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    </head>
    <body>
        <!-- 容器开始 -->


        <div class="span24">
            <div class="control-group">
                <label>合作方简称：</label>
                <input type="text" class="easyui-validatebox"
                       data-options="required:false" id="channelEntityName" name="channelEntityName"
                       style="width:80px"/>
                <a id="queryBtn1" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">查询</a>

            </div>
         </div>

            <div class="control-group span23"><!-- 承包商绑定微格表 -->
                <table id="contractorMatchSmallGrid" style="height:400px"
                       data-options="rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">
                    <thead>
                        <tr>
                            <th data-options="field:'channelEntityName',align:'center',exportable:false,width:100,hidden:false">
                                合作方简称
                            </th>
                            <th data-options="field:'smallGridName' ,align:'center',width:140">
                                微格名称
                            </th>
                            <th data-options="field:'countyName',align:'center',width:100,hidden:false">
                                微格所属分公司
                            </th>
                            <th data-options="field:'gridName',align:'center',width:120,hidden:false">
                                微格所属网格
                            </th>
                            <th data-options="field:'contractType',align:'center',width:100,formatter:function(value){
                                if(value=='SOCIAL_CHANNEL'){
                                return'社会渠道';
                                }else if(value=='HAVE_OWN'){
                                return'自有';}
                                }">微格属性</th>
                            <th data-options="field:'startTime',align:'center',width:130,formatter:function date(value) {var date = new Date(value);var year = date.getFullYear().toString();var month = (date.getMonth() + 1);var day = date.getDate().toString();
                                if (month < 10) {month = '0' + month;}
                                if (day < 10) {day = '0' + day;}
                                return year + '-' + month + '-' + day;}">承包开始时间
                            </th>
                            <th data-options="field:'endTime',align:'center',width:130,formatter:function date(value) {var date = new Date(value);var year = date.getFullYear().toString();var month = (date.getMonth() + 1);var day = date.getDate().toString();
                                if (month < 10) {month = '0' + month;}
                                if (day < 10) {day = '0' + day;}
                                return year + '-' + month + '-' + day;}">承包结束时间
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    <div class="span5"></div>

        <div class="span24">
            <div class="span7 control-group">
                <label>微格名称：</label>
                <input type="text" class="easyui-validatebox"
                       data-options="required:false" id="smallGridName" name="smallGridName"
                       style="width:80px"/>
                <a id="queryBtn2" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">查询</a>
            </div>
            <div class="span23"><!-- 微格绑定承包商信息表 -->
                <table class="easyui-datagrid" id="smallGridMatchContractor" style="height:400px"
                       data-options="rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">
                    <thead>
                        <tr>
                            <th data-options="field:'smallGridName',align:'center',width:140,hidden:false">
                                微格名称
                            </th>
                            <th data-options="field:'countyName',align:'center',width:100,hidden:false">
                                微格所属分公司
                            </th>
                            <th data-options="field:'gridName',align:'center',width:120,hidden:false">
                                微格所属网格
                            </th>
                            <th data-options="field:'contractType',align:'center',width:100,formatter:function(value){
                                if(value=='SOCIAL_CHANNEL'){
                                return'社会渠道';
                                }else if(value=='HAVE_OWN'){
                                return'自有';}
                                }">微格属性</th>
                            <th data-options="field:'channelEntityName',align:'center',width:100,hidden:false">
                                合作方简称
                            </th>
                            <th data-options="field:'startTime',align:'center',width:130,formatter:function date(value) {var date = new Date(value);var year = date.getFullYear().toString();var month = (date.getMonth() + 1);var day = date.getDate().toString();
                                if (month < 10) {month = '0' + month;}
                                if (day < 10) {day = '0' + day;}
                                return year + '-' + month + '-' + day;}">承包开始时间
                            </th>
                            <th data-options="field:'endTime',align:'center',width:130,formatter:function date(value) {var date = new Date(value);var year = date.getFullYear().toString();var month = (date.getMonth() + 1);var day = date.getDate().toString();
                                if (month < 10) {month = '0' + month;}
                                if (day < 10) {day = '0' + day;}
                                return year + '-' + month + '-' + day;}">承包结束时间
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>

        </div>


        <!-- 容器结束 -->


    </body>
    <script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
    <script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
    <script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
    <script type="text/javascript" src="${ctx}/js/common/config.js"></script>
    <script>
        BUI.use('module/archive/contractorManager/smallGridQuery', function (smallGridQuery) {
            new smallGridQuery();
        });
    </script>
</html>
