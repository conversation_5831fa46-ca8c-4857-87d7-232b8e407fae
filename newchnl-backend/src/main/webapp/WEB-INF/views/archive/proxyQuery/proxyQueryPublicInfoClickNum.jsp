
<%--代理查询公布信息
 * 
 * $Id: proxyQueryPublicInfoClickNum.jsp,v 1.1.2.2 2014/11/19 13:34:49 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>代理查询公布信息</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
		.form-horizontal .controls {
			height: 30px;
		}
		.form-horizontal .control-label {
			height: 30px;
			width: 100px;
			line-height: 20px;
		}
		
		input[type="text"], input[type="password"], input[type="email"]{
			width:100px;
		}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
   
    <!-- 负责人录入 -->
    <div region="center" title="" border="false" style="padding: 10px;">
    <div class="row">
	    <form class="form-horizontal"  id="ff">
                              代理查询公布信息 
              <hr/>
				<div class="row">
					 <div class="control-group span8">
						<label class="control-label">分公司：</label>
						<div class="controls">
							<select class="easyui-combobox" id="regoryCompany"
									data-options="panelHeight:'180', required:false,editable:false,
									valueField:'id',textField:'text'">
								 </select>
						</div>
					</div>
		 				<div class="control-group span8">
							<label class="control-label">代理商名称：</label>
							<div class="controls">
								<input id="ccagent" style="width:160px;" />
							</div>
							<div id="spagent">
								<div style="margin: 2px;">
									<span>
										<input id="keyagent" class="easyui-validatebox" style="width:90px;" /> 
									</span>
									<span>
										<a id="treeQueryBtnagent" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								<div style="margin: 2px;">
									<ul id="ttagent"></ul> 
								</div>
							</div>
						</div>
				</div>
			
				<div class="row">
					 <div class="control-group span8">
						<label class="control-label">起始时间：</label>
						<div class="controls">
							<select  class="easyui-datebox" id="startDate"> </select>
						</div>
					</div>
					
					<div class="control-group span8">
						<label class="control-label">终止时间：</label>
						<div class="controls">
							<select class="easyui-datebox" id="endDate"> </select>
							
						</div>
				  </div>
				</div>
				<div class="row">
					<div class="span8 offset8">
					
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="" id="searchBtn">查询</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="" id="resetBtn">重置</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="" id="exportBtn">导出</a>
					</div>
				</div>
			</form>
</div>
	
			<!-- 数据显示开始 -->
		<div class="row">
					 <table id="proxyQueryPublicInfoList" class="easyui-datagrid span24" style = "width:960px;"
      					 data-options="pageSize:10,height:380,pagination:true,fitColumns:false,striped:true,loadMsg:'加载中...'"> 
    				<thead>   
      				  	<tr>   
                        <th data-options="field:'ck',checkbox:true"></th>	
						<th data-options="field:'codeName',align:'center',width:'100'">分公司</th>
						<th data-options="field:'fullName',align:'center',width:'100'">代理商</th>
						<th data-options="field:'rewardQueryTimes',width:'200',align:'center'">网站/渠道代理商服务酬金查询次数</th>	
						<th data-options="field:'baseInfoQueryTimes',width:'140',align:'center'">渠道基础信息查询次数</th>	
						<th data-options="field:'payPlanQueryTimes',width:'140',align:'center'">代理商支付进度查询次数</th>	
						<th data-options="field:'policyQueryTimes',width:'120',align:'center'">政策类查询次数</th>	
						<th data-options="field:'mount',width:'120',align:'center'">总计</th>	
        				</tr>   
   				 </thead>   
			</table>
		</div>
		</div>
		<!-- 数据显示结束 -->
		<!-- 各分公司负责人信息修改 -->	
     
		<div id="showCompanyTree" class="easyui-window" title="弹出窗口组织结构"data-options="modal:true,closed:true,iconCls:'icon-edit',
			    minimizable:false,maximizable:false,resizable:false">
				<div class="container" >
					<div data-options="region:'align',split:true" border="false" title="上海移动"style="width: 400px; height:500px;padding: 10px;" id="actionTree"></div>
				</div>
	   </div>		

</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<!-- 年月日期控件 -->
<script type="text/javascript" src="${ctx}/js/module/archive/channelSupportOffce/yearMonthTime.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use('module/archive/proxyQuery/proxyQueryPublicInfoClickNum',function(ProxyQueryPublicInfoClickNum){
	new ProxyQueryPublicInfoClickNum();
});
</script>
</html>