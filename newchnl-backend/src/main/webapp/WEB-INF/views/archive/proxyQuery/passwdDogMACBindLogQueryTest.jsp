
<%--代理查询公布信息
 * 
 * $Id: passwdDogMACBindLogQueryTest.jsp,v 1.5 2014/11/21 14:16:12 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>代理查询公布信息</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
		.form-horizontal .controls {
			height: 30px;
		}
		.form-horizontal .control-label {
			height: 30px;
			width: 100px;
			line-height: 20px;
		}
		
		input[type="text"], input[type="password"], input[type="email"]{
			width:100px;
		}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
   
    <!-- 负责人录入 -->
    <div region="center" title="" border="false" style="padding: 10px;">
    <div class="row">
	    <form class="form-horizontal"  id="ff">
                              密码狗绑定MAC日志查询  
              <hr/>
				<div class="row">
				
					 <div class="control-group span8">
						<label class="control-label">分公司</label>
						<div class="controls">
							<select class="easyui-combobox" id="regoryCompany"
									data-options="panelHeight:'auto', required:false,editable:false,
									valueField:'id',textField:'text'">
								 </select>
						</div>
					</div>
		 				<div class="control-group span8">
							<label class="control-label">门店</label>
							<div class="controls">
								<input id="ccagent" style="width:160px;" />
							</div>
							<div id="spagent">
								<div style="margin: 2px;">
									<span>
										<input id="keyagent" class="easyui-validatebox" style="width:90px;" /> 
									</span>
									<span>
										<a id="treeQueryBtnagent" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								<div style="margin: 2px;">
									<ul id="ttagent"></ul> 
								</div>
							</div>
						</div>
				</div>
			
				<div class="row">
					 <div class="control-group span8">
						<label class="control-label">起始时间</label>
						<div class="controls">
							<select  class="easyui-datebox" id="startDate"> </select>
						</div>
					</div>
					
					<div class="control-group span8">
						<label class="control-label">终止时间</label>
							<div class="controls">
									<select class="easyui-datebox" id="endDate"> </select>
							</div>
					  </div>
				  		 <div class="control-group span6">
						<label class="control-label">绑定状态</label>
						<div class="controls">
								<select class="easyui-combobox" id="boundStatus"
									data-options="panelHeight:'auto', required:false,editable:false,
									valueField:'id',textField:'text'">
								 </select>
							</input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="span8 offset8">
					
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="searchBtn">查询</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-undo" id="resetBtn">重置</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-excel" id="exportBtn">导出</a>
					
					</div>
				</div>
			</form>
</div>
	
			<!-- 数据显示开始 -->
		<div class="row">
					 <table id="passwdDogMACBindLogQueryList" class="easyui-datagrid span24" style = "width:960px;"data-options="pageSize:20,height:380,pagination:false,scrollbarSize:18,striped:true,loadMsg:'加载中...'"> 
    				<thead>   
      				  	<tr>   
		                        <th data-options="field:'ck',checkbox:true"></th>	
								<!-- <th data-options="field:'m_strCompanyName',align:'center',width:'150'">分公司</th> -->
								<th data-options="field:'agentName',align:'center',width:'100'">代理商名称</th>
								<th data-options="field:'districtName',align:'center',width:'100'">分公司</th>
								<th data-options="field:'nodeName',align:'center',width:'100'">门店名称</th>
								<th data-options="field:'orgName',align:'center',width:'150'">CRM门店名称</th>
								<th data-options="field:'doneDate',align:'center',width:'150'">记录配置时间</th>
								<th data-options="field:'mac',align:'center',width:'150'">MAC地址</th>
								<th data-options="field:'ip',align:'center',width:'100'">IP地址</th>
								<th data-options="field:'employeeId',align:'center',width:'200'">绑定的专营店系统操作员工号</th>
								<th data-options="field:'operId',align:'center',width:'100'">配置记录的操作员工号</th>
								<!-- 状态flag改为ext1 -->
								<th data-options="field:'ext1',align:'center',width:'100'">状态</th>
							<!-- 	<th data-options="field:'flag',width:'70',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10013',value);}">状态</th> -->
								<th data-options="field:'outSet',align:'center',width:'100'">解绑次数</th>

        				</tr>   
   				 </thead>   
			</table>
		</div>
		</div>
		<!-- 数据显示结束 -->


</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<!-- 年月日期控件 -->
<script type="text/javascript" src="${ctx}/js/module/archive/channelSupportOffce/yearMonthTime.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use('module/archive/proxyQuery/passwdDogMACBindLogQueryTest',function(PasswdDogMACBindLogQueryTest){
	new PasswdDogMACBindLogQueryTest();
});
</script>
</html>