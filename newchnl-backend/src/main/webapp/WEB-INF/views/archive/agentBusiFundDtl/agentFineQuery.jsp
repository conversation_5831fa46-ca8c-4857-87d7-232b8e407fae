<%--
 * 
 * $Id: agentFineQuery.jsp,v 1.6 2014/10/10 08:30:22 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
<title>罚金查询</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
		.form-horizontal .controls {
			height: 30px;
		}
		.form-horizontal .control-label {
			height: 30px;
			width: 100px;
			line-height: 20px;
		}
		
		input[type="text"], input[type="password"], input[type="email"]{
			width:100px;
		}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
    <div data-options="region:'west',split:true"  border="false" title="组织树" style="width:200px; padding: 10px;" id="actionTree">
	</div>
    <!-- 罚金查询div -->
     <div region="center" title="" border="false" style="padding: 10px;">
			<div class="row">
				<form class="form-horizontal">
				<input id="regionid" type="hidden">
                       罚金查询
              <hr/>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">账号：</label>
						<div class="controls">
						  <input type="text" class="easyui-validatebox" data-options="required:false" id="accId" readonly="readonly"></input>

						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">科目：</label>
						<div class="controls">
							<input type="text" class="easyui-combobox"  id="accName" 
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input> 
						</div>
					</div>
				</div>
				<div class="row">
					<div class="span8 offset14">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">查询</a>
					</div>
				</div>
			</form>
			</div>
			<div id="tb">
			<div style="margin-bottom:8px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true" id="excleBtn">excle导出</a>
			</div>
		</div>
	 	  <div class="row" style="margin-left: 10px;">
		   <table id="agentFineQueryTable" class="easyui-datagrid span24" title="罚金数据显示" style="width: 850px;"
                data-options="pageSize:10,height:380,rownumbers:true,singleSelect:true,fitColumns:true,striped:true,loadMsg:'加载中...'">
                <thead>  
		          <tr>   
		            <th data-options="field:'accId',align:'center',width:100">帐号</th>
		            <th data-options="field:'accName',align:'center',width:100">科目</th>
		            <th data-options="field:'busiCodeName',align:'center',width:100">业务名称</th>
		            <th data-options="field:'lostCard',align:'center',width:70">外流卡数量</th>
		            <th data-options="field:'totalFeeStr',align:'center',width:100">金额(元)</th>
		            <th data-options="field:'doneDate',align:'center',width:140">操作日期</th>
		            <th data-options="field:'notes',align:'center',width:150">备注</th>
		          </tr>   
		        </thead>   
          </table>  
	  </div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use('module/archive/agentBusiFundDtl/agentFineQuery',function(AgentFineQuery){
	new AgentFineQuery();
});
</script>
</html>