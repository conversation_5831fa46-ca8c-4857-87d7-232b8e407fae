<%--
 * 
 * $Id: agentBustFundDtlAdd.jsp,v 1.7 2014/10/28 08:22:01 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
<title>罚金设置</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
		.form-horizontal .controls {
			height: 30px;
		}
		.form-horizontal .control-label {
			height: 30px;
			width: 100px;
			line-height: 20px;
		}
		
		input[type="text"], input[type="password"], input[type="email"]{
			width:100px;
		}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
    <div data-options="region:'west',split:true"  border="false" title="组织树" style="width:200px; padding: 10px;" id="actionTree">
	</div>
    <!-- 录入 -->
    <div region="center" title="" border="false" style="padding: 10px;">
		<form class="form-horizontal" id="ff" method="post">
		<input type="hidden" id="regionid" />
	       罚金设置
		        <hr />
		        <input name="agentId" id="agentId" type="hidden">
			  
				<div class="row">
					<div class="control-group span6">
						<label class="control-label">帐号：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" id="accId" readonly="readonly"></input>
						</div>
				  </div>
					 <div class="control-group span6">
						<label class="control-label">选择科目：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-combobox"  id="accName" 
								        data-options="panelHeight:'auto',
										required:false,
										editable:false,
										valueField:'id',
										textField:'text'"></input>
						</div>
					</div>
			 </div>
				<div class="row">
						<div class="control-group span6">
							<label class="control-label">外流卡数量：</label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" data-options="required:false"  id="lostCard"></input>
						</div>
		            </div>
				</div>
				<div class="row">
					<div class="control-group span6">
						<label class="control-label">应缴罚款(元)：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox"  precision="2"  id="totalFee"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span18">
						<label class="control-label">备注：</label>
						<div class="controls">
							<textarea rows="10" cols="10" id="notes"></textarea>
						</div>
					</div>
				</div>
				<div class="row">
				     <div class="offset4" style="padding:50px;" id="add">
						<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="saveBtn">保存</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-undo'" id="resetBtn">重置</a>
		        	</div>
		</div>	
			</form>
		</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use('module/archive/agentBusiFundDtl/agentBusiFundDtlAdd',function(AgentBusiFundDtlAdd){
	new AgentBusiFundDtlAdd();
});
</script>
</html>