<%--
 * 
 * $Id: agentBustFundDtlFrameset.jsp,v 1.10 2015/02/17 13:28:04 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
<title>罚金维护</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
		.form-horizontal .controls {
			height: 30px;
		}
		.form-horizontal .control-label {
			height: 30px;
			width: 100px;
			line-height: 20px;
		}
		
		input[type="text"], input[type="password"], input[type="email"]{
			width:100px;
		}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
    <div data-options="region:'west',split:true"  border="false" title="组织树" style="width:200px; padding: 10px;" id="actionTree">
	</div>
	
   <!-- 罚金维护 -->
     <div region="center" title="" border="false" style="padding: 10px;">
		<div class="row" >
			<form class="form-horizontal" id="ff" method="post">
				<input id="regionid" type="hidden">
                       罚金维护
              <hr/>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">账号：</label>
						<div class="controls">
						  <input type="text" class="easyui-validatebox" data-options="required:false" id="accId" readonly="readonly"></input>

						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">科目：</label>
						<div class="controls">
							<input type="text" class="easyui-combobox"  id="accName" 
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input> 
						</div>
					</div>
				</div>
				<div class="row">
					<div class="span8 offset16">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">查询</a>
					</div>
				</div>
			 </form>
		 </div>
		  <div id="tb">
			<div style="margin-bottom:8px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-cancel',plain:true" id="addBtn">罚款</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="paymentBtn">缴纳</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-redo',plain:true" id="particularBtn">明细</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-pencil',plain:true" id="updateBtn">修正</a>
			</div>
		</div>
	 	 <div class="row" style="margin-left: 10px;">
		   <table id="agentFineQueryTable" class="easyui-datagrid span24" title="罚金数据显示" style="width: 700px;"
                data-options="pageSize:10,height:340,pagination:true,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
                <thead>  
		          <tr>   
		            <th data-options="field:'ck',checkbox:true"></th>
		            <th data-options="field:'accId',align:'center',width:100">帐号</th>
		            <th data-options="field:'accName',align:'center',width:100">罚金名称</th>
		            <th data-options="field:'doneDate',align:'center',width:100">生效日期</th>
		            <th data-options="field:'totalFee',align:'center',width:100,formatter: function(value,row,index){return BUI.columnFormatterMoneny(value);}">金额(元)</th>
		           
		          </tr>   
		        </thead>   
          </table>  
	   </div>
	</div>
	<!-- 弹出层  罚款div -->
	<div id="agentBusiFunctionDtlFramesetWindow" class="easyui-window" title="罚金设置"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div class="container" >
			<div class="row" style="padding:8px; height:auto">
				<form class="form-horizontal" id = "ff1">
					<input type="hidden" id="central_id"></input>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">帐号：<s>*</s></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="acc_id" disabled="disabled"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">科目：<s>*</s></label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="acc_name" 
								    disabled="disabled"
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">外流卡数量：</label>
							<div class="controls">
								<input type="text" class="easyui-numberbox" data-options="required:false"  id="lost_card"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">罚款：<s>*</s></label>
							<div class="controls">
								<input type="text" class="easyui-numberbox"  precision="2" id="total_fee"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">备注：</label>
								<textarea rows="10" cols="10" id="no_tes"></textarea>
						</div>
					</div>
					<div class="row">
						<div class="offset3">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="savesBtn">确定</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	<!-- 弹出层  缴纳罚款div -->
	<div id="agentBusiFunctionDtlFramesetPaymentWindow" class="easyui-window" title="罚金缴纳"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div class="container" >
			<div class="row" style="padding:8px; height:auto">
				<form class="form-horizontal" id="ff2">
					<input type="hidden" id="central_id"></input>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">帐号：<s>*</s></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="payment_accId" disabled="disabled"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">科目：<s>*</s></label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="payment_accName" 
								    disabled="disabled"
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">罚款(元)：<s>*</s></label>
							<div class="controls">
								<input type="text" class="easyui-numberbox"  precision="2" id="payment_totalFee"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">备注：</label>
								<textarea rows="10" cols="10" id="payment_notes"></textarea>
						</div>
					</div>
					<div class="row">
						<div class="offset3">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="payment_savesBtn">确定</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="payment_cancleBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
	<!-- 明细  -->
	<div id="agentBusiFunctionDtlParticularWindow" class="easyui-window" title="罚金历史记录查看"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		 <div id="tb">
			<div style="margin-bottom:8px">
			   <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-excel" id="particular_XlssavesBtn">导出Excel</a>
			   <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="particular_XlscancleBtn">后退</a>
			</div>
		</div>
		 <div class="row" style="margin-left: 10px;">
			 <table id="agentFineQueryParticularTable" class="easyui-datagrid span24" title="正数的金额为缴纳的罚金，负数的金额为罚款的金额"
                data-options="pageSize:10,height:300,pagination:true,width:600,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
                <thead>  
		          <tr>   
		            <th data-options="field:'accId',align:'center',width:100">帐号</th>
		            <th data-options="field:'accName',align:'center',width:100">科目</th>
		            <th data-options="field:'busiCodeName',align:'center',width:100">业务名称</th>
		            <th data-options="field:'totalFeeStr',align:'center',width:100">金额(元)</th>
		            <th data-options="field:'doneDate',align:'center',width:120">操作日期</th>
		            <th data-options="field:'notes',align:'center',width:150">备注</th>
		          </tr>   
		        </thead>   
          </table>  
		</div>
	</div>
	<!-- 修正  弹出层 -->
	<div id="agentBusiFunctionDtlUpdateWindow" class="easyui-window" title="罚金修正"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div class="container" >
			<div class="row" style="padding:8px; height:auto">
				<form class="form-horizontal" id ="ff3">
					<input type="hidden" id="central_id"></input>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">帐号：<s>*</s></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="update_accId" disabled="disabled"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">科目：<s>*</s></label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="update_accName" 
								    disabled="disabled"
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">罚款类型：<s>*</s></label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="update_totalFeeJuede" 
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">罚款(元)<span style="color:red;">*</span>：</label>
							<div class="controls">
								<input type="text" class="easyui-numberbox"  precision="2" id="update_totalFee"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">备注：</label>
								<textarea rows="10" cols="10" id="update_notes"></textarea>
						</div>
					</div>
					<div class="row">
						<div class="offset3">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="update_savesBtn">确定</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="update_cancleBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use('module/archive/agentBusiFundDtl/agentBusiFundDtlFrameset',function(AgentBusiFundDtlFrameset){
	new AgentBusiFundDtlFrameset();
});
</script>
</html>