<%--
 * 
 * $Id: agentFineUpload.jsp,v 1.10 2015/02/18 15:18:34 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
<title>罚金批量导入</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
  
</head>
<body>
	<div class="container span24">
		<div class="row" style="padding:8px; height:auto">
			<form class="form-horizontal" id="ff" method="post" >
				<div class="row">
					<div class="control-group span12">
						<label class="control-label">导入罚金文件：</label>
						<div class="controls">
							<input type="file" class="easyui-validatebox"  data-options="required:false" id="fileinput" name="fileName"  />
						</div>
				  </div>
				<div class="row control-group span12">
				 	<label class="control-label">模版示例:</label>
				 	<div class="controls">
				 	<a href="#" id="agentFineUpload">罚金导入信息模板</a>
				 	</div>
				 </div>
				</div>
				<div class="row control-group offset14">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-ok" id="uploadBtn">导入</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="saveBtn">保存</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-reload" id="resetLBtn">重置</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" id="cancelBtn">取消</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-excel" id="xlsBtn">导出错误信息</a>
				</div>
			</form>
		</div>
		<hr/>
		<!-- txt模版数据 -->
		<div class="span24">
				 <table id="agentFineUploadModelTable" class="easyui-datagrid span24" title="模版数据"
                data-options="pageSize:10,height:300,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
                <thead>  
		          <tr>   
		            <th data-options="field:'ck',checkbox:true"></th>
		            <th data-options="field:'channelEntityName',align:'center',width:100">合作方名称</th>
		            <th data-options="field:'lostCard',align:'center',width:60">外流卡数量</th>
		            <th data-options="field:'totalFee',align:'center',width:100">金额(元)</th>
		          </tr>   
		        </thead>   
          </table> 
		</div>
		
		<!-- 导入结果 -->
		<div class="span9">
				 <table id="agentFineUploadTable" class="easyui-datagrid span24" title="正确文本数据"
                data-options="pageSize:10,height:300,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
                <thead>  
		          <tr>   
		            <th data-options="field:'channelEntityName',align:'center',width:100">合作方名称</th>
		            <th data-options="field:'lostCard',align:'center',width:60">外流卡数量</th>
		            <!-- <th data-options="field:'totalFee',align:'center',width:100,formatter: function(value,row,index){return BUI.columnFormatterMoneny(value);}">金额(元)</th> -->
		            <th data-options="field:'totalFee',align:'center',width:100">金额(元)</th>
		          </tr>   
		        </thead>   
          </table> 
		</div>
		 <div class="span15">
				 <table id="agentFineUploadErrorTable" class="easyui-datagrid span24" title="错误文本数据"
                data-options="pageSize:10,height:300,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
                <thead>  
		          <tr>   
		             <th data-options="field:'ck',checkbox:true"></th>
		            <th data-options="field:'channelEntityName',align:'center',width:100">合作方名称</th>
		            <th data-options="field:'lostCard',align:'center',width:60">外流卡数量</th>
		            <th data-options="field:'totalFee',align:'center',width:50">金额(分)</th>
		            <th data-options="field:'notes',align:'center',width:200">错误原因</th>
		          </tr>     
		        </thead>   
          </table> 
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/agentBusiFundDtl/agentFineUpload',function(AgentFineUpload){
		new AgentFineUpload();
	});
</script>
</html>