
<%--
 * 
 * $Id: showAgentInfoCheckView.jsp,v 1.3 2014/09/16 12:13:28 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 * 代理商审核信息查看
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
<style type="text/css">
	.form-horizontal .control-label {
		width: 120px;
	}
	
	input[type="text"],input[type="password"],input[type="email"]{
		width: 150px;
		padding: 0px;
	}
	
	select{
		width: 150px;
		padding: 0px;
	}
</style>
</head>
<body>
	<div class="container">
		<form id="ff" class="form-horizontal" title="代理商信息查看" method="post">
			<input type="hidden" id="channelEntityId" value="<%=request.getAttribute("channelEntityId") %>" />
			<input type="hidden" id="channel_entity_status_in" value="<%=request.getAttribute("channelEntityStatus")%>"/>
			<input type="hidden" name="operateType" />
			<input type="hidden" name="channelEntityType" />
			<input type="hidden" name="channelEntitySerial" />
			
			<input type="hidden" name="remark" />
			<input type="hidden" name="signBeginDate" />
			<input type="hidden" name="signEndDate" />
			<fieldset class="span24">
				<legend>基本信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">代理商名称：</label>
						<div class="controls">
							<input type="text" name="channelEntityName"
								id="bean_channel_entity_name" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">代理商全称：</label>
						<div class="controls">
							<input type="text" name="fullName" id="bean_full_name" disabled="disabled"></input>
						</div>
					</div>
						<div class="control-group span8">
						<label class="control-label">代理商等级：</label>
						<div class="controls">
							<select class="easyui-combobox" name="agentLevel"
								data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="bean_agentLevel" disabled="disabled"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">分公司:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="parentEntity"
								data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="parent_Entity" disabled="disabled"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">行业业态：</label>
						<div class="controls">
							<select class="easyui-combobox" name="industryType"
								data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="bean_industry_type" disabled="disabled"></select>
						</div>
					</div>
				</div>
			</fieldset>

			<fieldset class="span24">
				<legend>企业信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">企业性质：</label>
						<div class="controls">
							<select class="easyui-combobox" name="corporationCharacter"
								data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="bean-corporationCharacter" disabled="disabled"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">网点数：</label>
						<div class="controls">
							<input type="text" name="nodeNum" id="bean_node_num" disabled="disabled"></input>
						</div>
					</div>
						<div class="control-group span8">
						<label class="control-label">注册资金：</label>
						<div class="controls">
							<input type="text" name="corporationFund" disabled="disabled"
								id="bean_corporation_fund"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">企业法人：</label>
						<div class="controls">
							<input type="text" name="rel_relationName_2" disabled="disabled"
								id="bean_relation_name"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">邮编：</label>
						<div class="controls">
							<input type="text" name="postCode" id="bean_post_code" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">归属组织：</label>
						<div class="controls">

							<select class="easyui-combobox" name="districtId"
								data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="bean_district_id" disabled="disabled"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">行政区：</label>
						<div class="controls">
							<select class="easyui-combobox" name="regionId"
								data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="bean_region_id" disabled="disabled"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">地址:</label>
						<div class="controls">
							<input type="text" name="address" id="bean_address" disabled="disabled"></input>
						</div>
					</div>
						<div class="control-group span8">
						<label class="control-label">工商号：</label>
						<div class="controls">
							<input type="text" name="businessNo" id="bean_business_no" disabled="disabled"></input>
						</div>
					</div>
				</div>
			</fieldset>
			<fieldset class="span24">
				<legend>代理商状态</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">代理商状态：</label>
						<div class="controls">
							<input type="text" name="channelEntityStatus"
								disabled="disabled" id="channelEntityStatus" value="申请"></input>
						</div>
					</div>
				</div>
			</fieldset>			
		</form>

		<!-- 审批结果 -->
		<fieldset class="span24">
			<form class="form-horizontal" id="submit" method="post">
				<legend>审批信息</legend>
				<div class="row">
					<div class="span8 control-group">
						<label class="control-label">审批结果：</label>
						<div class="controls">
							<select class="easyui-combobox" name="checkResult"
								data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="checkResult"></select>
						</div>
					</div>
					<div class="control-group span16">
						<label class="control-label">批审意见：</label>
						<div class="controls">
							<input type="textarea" name="checkDesc" id="checkDesc"></input>
						</div>
					</div>
				</div>
			</form>
			<div class="row">
				<div class="row offset11">
					<a id="save" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-save'">提交</a> 
					<a id="cheackClose" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'">关闭</a>
				</div>
			</div>
		</fieldset>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/agent/showAgentInfoCheckView',function(ShowAgentInfoCheckView){
		new ShowAgentInfoCheckView();
	});
</script>
</html>