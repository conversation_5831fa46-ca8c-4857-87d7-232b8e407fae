<%--
 * 
 * $Id: editApplicationAgentInfoModify.jsp,v 1.5 2015/02/05 00:26:53 gongchao Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 * 代理商信息修改
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>代理商信息修改</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css"href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
<style type="text/css">
	.form-horizontal .control-label {
		width: 120px;
	}
	input[type="text"],input[type="password"],input[type="email"]{
		width: 150px;
		padding: 0px;
	}
	select{
		width: 150px;
		padding: 0px;
	}
</style>
</head>
<body>
	<div class="container">
		<form id="ff" class="form-horizontal" title="代理商信息查看" method="post">
			<input type="hidden" id="channel_entity_id_in" value="<%=request.getAttribute("channelEntityId")%>" />
			<input type="hidden" id="channel_entity_status_in" value="<%=request.getAttribute("channelEntityStatus")%>" />
			<input type="hidden" id="channel_entity_id_in" />
			<input type="hidden" name="channelEntityId" />
			<input type="hidden" name="operateType" value="Upd" />
			<input type="hidden" name="channelEntityType" />
			<input type="hidden" name="channelEntitySerial" />
			<fieldset class="span24">
				<legend>基本信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">代理商名称:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" name="fullName"
								id="bean_full_name" data-options="required:true"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">代理商简称:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								name="channelEntityName" id="bean_channel_entity_name "
								data-options="required:true"></input>
						</div>
					</div>


					<div class="control-group span8">
						<label class="control-label">代理商等级:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="agentLevel"
								data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="bean_agentLevel"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">签约时间:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" data-options="required:true,editable:false"
								name="signBeginDate" id="signBeginDate" class="easyui-datebox"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">行业业态:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="industryType"
								data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text',required:true" 
								id="bean_industry_type"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">协议截止时间:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" data-options="required:true,editable:false"
								name="signEndDate" id="signEndDate" class="easyui-datebox"></input>
						</div>
					</div>
				</div>
			</fieldset>
			
			
			<fieldset class="span24">
				<legend>企业信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">企业性质:  <span style="color:red;">*</span></label>
						<div class="controls">

							<select class="easyui-combobox" class="easyui-combobox"
								name="corporationCharacter"
								data-options="required:true,panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="bean_corporationCharacter"></select>

						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">网点数:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" name="nodeNum"
								id="bean_node_num" data-options="required:true"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">注册资金:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox"
								data-options="required:true" name="corporationFund"
								id="bean_corporation_fund"></input>
						</div>
					</div>
				</div>

				<div class="row">

					<div class="control-group span8">
						<label class="control-label">企业法人:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="rel_relationName_2"
								id="bean_relation_name"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">邮编:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" data-options="required:true,validType:'postCode'" name="postCode" id="bean_post_code"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">归属组织:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="districtId"
								data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="bean_district_id"></select>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="control-group span8">
						<label class="control-label">行政区:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="regionId"
								data-options="required:true,panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="bean_region_id"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">地址:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="address" id="bean_address"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">工商号:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="businessNo"
								id="bean_business_no"></input>
						</div>
					</div>
				</div>
			</fieldset>


			<fieldset class="span24">
				<legend>代理商状态</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">代理商状态:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="channelEntityStatus" disabled="disabled"
								id="bean_channel_entity_status" value="申请"></input>
						</div>
					</div>
				</div>
			</fieldset>

			<!-- 财务信息 -->
			<fieldset class="span24">
				<legend>财务信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">收取酬金银行(银行编码):  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								   data-options="required:true" name="bankName"
								   id="bankName" readonly="readonly" placeholder=""></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">收取酬金银行支行:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="bank_bankBranch_2"
								id="bean_BANK_BRANCH_2" readonly="readonly"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">收取酬金银行帐户名:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" data-options="required:true"
								class="easyui-validatebox" name="bank_accName_2"
								id="bean_ACC_NAME_2" readonly="readonly"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">收取酬金银行帐号:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="bank_bankAccount_2"
								id="bean_BANK_ACCOUNT_2" readonly="readonly"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">收取酬金户主身份证:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:true" name="bank_ext1_2" id="bean_EXT1_2"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">缴款银行(银行编码):  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="bank_bankCode_3" data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="bean_BANK_CODE_3"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">缴款银行支行:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:true" name="bank_bankBranch_3"
								id="bean_BANK_BRANCH_3"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">缴款银行帐户名:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:true" name="bank_accName_3"
								id="bean_ACC_NAME_3"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">缴款银行帐号:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="bank_bankAccount_3"
								id="bean_BANK_ACCOUNT_3"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">缴款银行开卡绑定的手机号:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:true,validType:'phone'" name="bank_ext2_3" id="bean_EXT2_3"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">订单系统对应联系人姓名:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:true" name="rel_relationName_7" id="bean_RELATION_NAME_7"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">订单系统对应联系人电话:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:true,validType:'phone'" name="rel_relationMobile_7" id="bean_RELATION_MOBILE_7"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">订单系统对应联系人职位:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:true" name="rel_pos_7" id="bean_POS_7"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">担保人姓名或公司:</label>
						<div class="controls">
							<input type="text" name="rel_relationName_4" id="bean_RELATION_NAME_4"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">担保人职位:</label>
						<div class="controls">
							<input type="text" name="rel_pos_4" id="bean_POS_4"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">缴款方式:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="paymentManner" data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text',required:true"
								id="bean_payment_manner"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">纳税人类别:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="taxpayerType" data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="bean_taxpayer_type"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">开票类别:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="invoiceType" data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="bean_invoice_type"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">供应商身份证号:  </label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" name="identifyCode" id="identify_code" readonly="readonly"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">法人代表:  </label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" name="legalRepresentative" id="legal_representative" readonly="readonly"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">统一社会信用代码:  </label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" name="socIden" id="soc_iden" readonly="readonly"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<a id="agent_news_connection" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-save'">代理商银行信息关联</a>
					</div>
				</div>
				<%--供应商名称--%>
				<input type="hidden" name="vendorName" id="vendor_name"></input>
				<%--供应商编码--%>
				<input type="hidden" name="mdCode" id="md_code"></input>
				<%--是否内部关联方--%>
				<input type="hidden" name="innerFlag" id="inner_flag"></input>
				<%--内部关联方标识--%>
				<input type="hidden" name="innerCode" id="inner_code"></input>
				<%--供应商状态--%>
				<input type="hidden" name="vendorStatus" id="vendor_status"></input>
				<%--所属公司--%>
				<input type="hidden" name="ouCode" id="ou_code"></input>
				<%--银行编码--%>
				<input type="hidden" name="bank_bankCode_2" id="bean_BANK_CODE_2"></input>
				<%--支行编码--%>
				<input type="hidden" name="branchCode" id="branch_code"></input>
				<%--联行号--%>
				<input type="hidden" name="cnapNumber" id="cnap_number"></input>
				<%--机构组织类型--%>
				<input type="hidden" name="corporationType" id="corporation_type"></input>
				<%--&lt;%&ndash;身份证号码&ndash;%&gt;--%>
				<%--<input type="hidden" name="identifyCode" id="identify_code"></input>--%>
			</fieldset>

			<!-- 业务联系人信息(选择输入) -->
			<fieldset class="span24">
				<legend>业务联系人信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">业务联系人:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="rel_relationName_1"
								id="bean_RELATION_NAME_1"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">联系人电话:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								name="rel_relationMobile_1"
								data-options="required:true,validType:'phone'"
								id="bean_RELATION_MOBILE_1"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">其他联系方式:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" name="rel_otherContact_1" data-options="required:true"
								id="bean_OTHER_CONTACT_1"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">联系人邮箱:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="rel_email_1" id="bean_EMAIL_1"
								class="easyui-validatebox"
								data-options="required:true,validType:'email'"></input>
						</div>
					</div>
				</div>
			</fieldset>
			<!-- 代理商服务号码 -->
			<fieldset class="span24">
				<legend>代理商服务号码<!--<input name="selectRelationManCheckbox" type="checkbox" value="on" id="selectBox"/> --> </legend>
				
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">用户名称:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								 name="rel_relationName_9"
								id="bean_relationName_9" data-options="required:true" maxlength="10"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">服务指定号码1:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								name="rel_relationMobile_9" 
								id="bean_relationMobile_9" data-options="required:true,validType:'phone'" ></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">服务指定号码2: </label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								name="rel_otherContact_9" 
								id="bean_otherContact_9" data-options="validType:'phone'" ></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">联系人地址:  </label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" name="rel_homeAddress_9" id="bean_homeAddress_9"></input>
						</div>
					</div>
				</div>
				
			</fieldset>
			
			<!-- 客户经理信息(选择输入) -->
			<fieldset class="span24">
				<legend>客户经理信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">分管客户经理:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="rel_relationName_5"
								data-options="required:true" class="easyui-validatebox"
								id="bean_RELATION_NAME_5"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">客户经理电话:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="rel_relationMobile_5"
								class="easyui-validatebox"
								data-options="required:true,validType:'phone'"
								id="bean_RELATION_MOBILE_5"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">客户经理邮箱:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="rel_email_5" id="bean_EMAIL_5"
								class="easyui-validatebox"
								data-options="required:true,validType:'email'"></input>
						</div>
					</div>
				</div>
			</fieldset>
		</form>
		
		<div class="row span24">
			<div class="row offset11">
				<a id="save" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-save'">提交</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" id="closeWindow">关闭</a>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.validatebox.extends.js"></script>
<script>
	BUI.use('module/archive/agent/editApplicationAgentInfoModify', function(
			EditApplicationAgentInfoModify) {
		new EditApplicationAgentInfoModify();
	});
</script>
</html>