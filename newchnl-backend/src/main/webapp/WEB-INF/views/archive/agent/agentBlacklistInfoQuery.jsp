<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@include file="/common/header.jsp" %>
<html>
<head>
    <title>代理商黑名单查询</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
    <!--容器开始-->
    <div class="container span24">
        <div class="row">
            <form class="form-horizontal">
                <div class="row">
                    <div class="span8 control-group">
                        <label class="control-label">合作方全称：</label>
                        <div class="controls">
                            <input type="text" class="easyui-validatebox"
                                   data-options="required:false" id="full_name" />
                        </div>
                    </div>
                    <div class="span8 control-group">
                        <label class="control-label" style="width: 120px;">合作方工商登记号：</label>
                        <div class="controls">
                            <input type="text" class="easyui-validatebox"
                                   data-options="required:false" id="business_no" />
                        </div>
                    </div>
                    <div class="span8 control-group">
                        <label class="control-label">录入人单位：</label>
                        <div class="controls">
                            <select class="easyui-combobox"
                                    data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
                                    id="org_id">
                            </select>
                        </div>
                    </div>
                </div>
            </form>

            <div class="row  offset12" >
                <a href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
            </div>
        </div>

        <!-- operate 开始 -->
        <div id="tb">
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true" id="exportAll">导出</a>
        </div>

        <!--数据回显-->
        <div class="row" style="margin-top: 10px">
            <table id="agentBlacklistInfo" title="查询结果" class="span24"
                   data-options="rownumbers:true,singleSelect:false"
                   style="height: 400px">
                <thead>
                    <tr>
                        <th data-options="field:'agentFullName',width:'200',align:'center',exportable:'true'">合作方全称</th>
                        <th data-options="field:'agentBusinessNo',width:'180',align:'center',exportable:'true'">合作方工商登记号</th>
                        <th data-options="field:'createDate',width:'180',align:'center',exportable:'true'">黑名单录入时间</th>
                        <th data-options="field:'opName',width:'180',align:'center',exportable:'true'">录入人</th>
                        <th data-options="field:'orgId',width:'180',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50445',value);},exportable:'true'">录入人单位</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>

<script>
    var _orgId = "<%= request.getAttribute("orgId") %>";
    var _orgName = "<%= request.getAttribute("orgName") %>";
    BUI.use('module/archive/agent/agentBlacklistInfoQuery',function(AgentBlacklistInfoQuery){
        new AgentBlacklistInfoQuery();
    });
</script>
</html>
