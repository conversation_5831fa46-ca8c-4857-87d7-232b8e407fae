<%--
 * 
 * $Id: agentServiceNum.jsp,v 1.3 2014/09/03 09:00:55 linfeng Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>代理商服务号码管理</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<!-- 容器开始 -->
	<div class="container">
		<div class="row" style="padding:8px; height:auto">
			<div class="row">
				<form class="form-horizontal">
					<div class="row">
						<div class="control-group span8">
							<input type="radio" name="agentType" id ="agentChecked" checked="checked" value="1"></input>
							<label class="control-label">代理商名称：</label>
							<div class="controls">
								<input id="ccagent" style="width:160px;" />
							</div>
							<div id="spagent">
								<div style="margin: 2px;">
									<span>
										<input id="keyagent" class="easyui-validatebox" style="width:90px;" /> 
									</span>
									<span>
										<a id="treeQueryBtnagent" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								<div style="margin: 2px;">
									<ul id="ttagent"></ul> 
								</div>
							</div>
						</div>
					<div class="control-group span8">
						<input type="radio" name="agentType" id ="nodeChecked" value="2"></input>
						<label class="control-label">网点名称：</label>
						<div class="controls">
							<input id="ccnode" style="width:160px;" />
						</div>
						<div id="spnode">
							<div style="margin: 2px;">
								<span>
									<input id="keynode" class="easyui-validatebox" style="width:90px;" /> 
								</span>
								<span>
									<a id="treeQueryBtnnode" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
								</span>
							</div>
							<div style="margin: 2px;">
								<ul id="ttnode"></ul> 
							</div>
						</div>
					</div>
					<div class="span8 control-group">
						<label class="control-label">是否已录入信息：</label>
						<div class="controls">
								<select class="easyui-combobox" name="districtId" data-options="panelHeight:'80',editable:false,valueField:'id',textField:'text'" id="hasAgentSvrNum"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="row offset12">
						<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
					</div>
				</div>
				</form>
			</div>
		</div>
		<!-- operate 开始 -->
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton"
					data-options="iconCls:'icon-remove',plain:true" id="delete">删除</a>	
				<a href="javascript:void(0)" class="easyui-linkbutton"
					data-options="iconCls:'icon-save',plain:true" id="export">导出</a>
			</div>
		</div>
		<!-- operate 结束 -->
		<!-- 数据显示开始 -->
		<div class="row span24">
			<table id="agentSvrNumData" title="查询结果" data-options="pageSize:10,height:400,rownumbers:true,singleSelect:true,fitColumns:true,striped:true,loadMsg:'加载中...'">
			    <thead>
			        <tr>
			        	<th data-options="field:'ck',checkbox:true"></th>
			       		<th data-options="field:'channelEntityId',hidden:true">实体编号</th>
			       		<th data-options="field:'relationType',hidden:true">联系人类型</th>
			        	<th	data-options="field:'channelEntityName',width:'170',align:'center'">渠道名称</th>
			        	<th	data-options="field:'relationName',width:'170',align:'center'">联系人</th>
			        	<th	data-options="field:'relationMobile',width:'170',align:'center'">服务号码一</th>
			        	<th	data-options="field:'otherContact',width:'170',align:'center'">服务号码二</th>
			        	<th	data-options="field:'homeAddress',width:'170',align:'center'">住址</th>
			        </tr>
			    </thead>
			</table>
		</div>
		<!-- 数据显示结束 -->
	</div>
	<!-- 容器结束 -->
	<!-- 弹出层开始 -->
	<div id="entityRelationWindow" class="easyui-window" title="联系人信息编辑"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div class="container" >
			<div class="row" style="padding:8px; height:170px ;width:470px">
				<form class="form-horizontal" id="ff" method="post">
					<input type="hidden" name="channelEntityId"></input>
					<input type="hidden" name="relationType"></input>
					<div class="row">
						<div class="control-group span6">
							<label class="control-label">渠道名称 ：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" disabled="disabled" name="channelEntityName"></input>
							</div>
						</div>
						<div class="control-group span6">
							<label class="control-label">联系人：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"  name="relationName"></input>
							</div>
						</div>
					</div>
					
					<div class="row">
						<div class="control-group span6">
							<label class="control-label">服务号码一 ：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"  name="relationMobile"></input>
							</div>
						</div>
						<div class="control-group span6">
							<label class="control-label">服务号码二：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"  name="otherContact"></input>
							</div>
						</div>
					</div>
					
					<div class="row">
						<div class="control-group span12">
							<label class="control-label">住址：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" style="width: 340px"  name="homeAddress"></input>
							</div>
						</div>
					</div>
					<div class="row" >
						<div class="offset5">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="saveBtn">保存</a>
							
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	<!-- 弹出层结束 -->
	
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.validatebox.extends.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/plugins/jquery.edatagrid.js"></script>
<script>
BUI.use('module/archive/agent/agentServiceNum',function(AgentServiceNum){
	new AgentServiceNum();
});
</script>
</html>