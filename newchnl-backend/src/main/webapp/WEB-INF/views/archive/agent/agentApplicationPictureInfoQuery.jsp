<%--
  Created by IntelliJ IDEA.
  User: asus
  Date: 2022/9/29
  Time: 15:23
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/common/header.jsp" %>
<html>
<head>
    <title>合作方申请信息图片详情</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
    <div class="container">
        <form id="ff" class="form-horizontal" method="post">
            <input type="hidden" id="phoneNum" name="phoneNum"  value="<%=request.getAttribute("phoneNum") %>"/>
            <fieldset class="span24">
                <legend>材料图片详情</legend>
                <div class="row">
                    <div class="control-group span12">
                        <label class="control-label">订单编号：  </label>
                        <input type="text" style="width: auto" class="easyui-validatebox"  id="agentId" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("agentId") %>"/>
                    </div>
                    <div class="control-group span12">
                        <label class="control-label">材料类型：  </label>
                        <input type="text" style="width: auto"  class="easyui-validatebox" id="fileType" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("fileType") %>"/>
                    </div>
                </div>

                <br>

            <div class="row">
                <div class="control-group span24">
                    <label class="control-label">材料详情图片： </label>
                    <br>
                    <input type="hidden" style="width: 100px" class="easyui-validatebox" id="fileInfo" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("fileInfo") %>"/>
                    <br>
                    <img src="<%=request.getAttribute("fileInfo")%>" alt="该图片格式错误！！！" class="img-responsive img-thumbnail">
                    <br>
                </div>
            </div>
            </fieldset>
        </form>
        <div class="row span24">
            <div class="row offset12">
                <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" id="closeWindow">关闭</a>
            </div>
        </div>
    </div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_export.js"></script>
<script>
    BUI.use("module/archive/agent/agentApplicationPictureInfoQuery", function (agentApplicationPictureInfoQuery) {
        new agentApplicationPictureInfoQuery();
    });
</script>
</html>

