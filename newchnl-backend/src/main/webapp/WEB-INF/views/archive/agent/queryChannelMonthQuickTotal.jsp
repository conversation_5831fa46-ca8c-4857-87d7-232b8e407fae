<%--
  Created by IntelliJ IDEA.
  User: dull
  Date: 2023/05/10
  Time: 14:11
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/common/header.jsp" %>
<html>
    <head>
        <title>直供渠道月度快速结算专项额度查询</title>
        <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
        <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    </head>
    <body>
            <div class="control-group span24 "><!-- 筛选条件 -->
                <div class="row">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="span8 control-group">
                            <label class="control-label">归属组织:  <span style="color:red;">*</span></label>
                            <div class="controls">
                                <select class="easyui-combobox" name="districtId"
                                        data-options="required:true,panelHeight:'300',editable:false,valueField:'id',textField:'text'"
                                        id="bean_district_id"></select>
                            </div>
                        </div>
                        <div>
                            <div class="span8 control-group">
                                <label class="control-label">月份：<span style="color:red;">*</span></label>
                                <select  class="easyui-datebox span4"   id="billMonth"> </select>
                            </div>
                        </div>
                        <div class="control-group span4">
                            <a href="javascript:void(0)" class="easyui-linkbutton"
                               data-options="iconCls:'icon-search'" id="queryBtn">查询</a>
                        </div>
                    </div>
                </form>
                </div>
                <!-- operate 部分 -->
                <a href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-excel',plain:true" id="export">导出</a>
                <!-- operate 结束 -->

                <!-- 数据显示  -->
                <div class="row span24">
                    <table class="easyui-datagrid" id="queryResultInfo" style="height:auto" title="查询结果列表"
                           data-options="rownumbers:true,singleSelect:false,fitColumns:false,striped:true,loadMsg:'加载中...'">
                        <thead>
                        <tr>
                            <%--<th data-options="field:'ck',checkbox:true"></th>--%>
                            <th data-options="field:'districtName',width:'150',align:'center'">归属组织</th>
                            <th data-options="field:'channelEntityName',align:'center',width:300">
                                网点名称
                            </th>
                            <th data-options="field:'billMonth' ,align:'center',width:170">
                                月份
                            </th>
                            <th data-options="field:'truncAmount' ,align:'center',width:300">
                                月度快速结算专项额度
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>


    </body>
    <script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
    <script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
    <script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
    <script type="text/javascript" src="${ctx}/js/common/config.js"></script>
    <script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_export.js"></script>
    <script>
        BUI.use("module/archive/agent/queryChannelMonthQuickTotal", function (queryChannelMonthQuickTotal) {
            new queryChannelMonthQuickTotal();
        });
    </script>
</html>
