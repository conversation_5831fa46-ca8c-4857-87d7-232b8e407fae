<%--
  Created by IntelliJ IDEA.
  User: asus
  Date: 2022/9/29
  Time: 15:23
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/common/header.jsp" %>
<html>
<head>
    <title>合作方申请信息详情</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
    <div class="container">
        <form id="ff" class="form-horizontal" method="post">
            <input type="hidden" id="phoneNum" name="phoneNum"  value="<%=request.getAttribute("phoneNum") %>"/>
            <fieldset class="span24">
                <legend>材料详情</legend>
                <div class="row">
                    <div class="control-group span12">
                        <label class="control-label">订单编号：  </label>
                        <input type="text" style="width: auto" class="easyui-validatebox"  id="agentId" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("agentId") %>"/>
                    </div>
                    <div class="control-group span12">
                        <label class="control-label">公司名称：  </label>
                        <input type="text" style="width: auto"  class="easyui-validatebox" id="channelEntityName" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("channelEntityName") %>"/>
                    </div>
                </div>

                <br>
                <div class="row">
                <div class="control-group span12">
                    <label class="control-label">联系人：  </label>
                    <input type="text" style="width: auto" class="easyui-validatebox"  id="relationName" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("relationName") %>"/>
                </div>
                <div class="control-group span12">
                    <label class="control-label">手机号码：  </label>
                    <input type="text" style="width: auto" class="easyui-validatebox"  id="relationMobile" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("relationMobile") %>"/>
                </div>
            </div>

             <br>
            <div class="row">
                <div class="control-group span12">
                    <label class="control-label">联系人邮箱：  </label>
                    <input type="text" style="width: auto" class="easyui-validatebox"  id="relationEmail" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("relationEmail") %>"/>
                </div>
                <div class="control-group span12">
                    <label class="control-label">合作申请说明：</label>
                    <input type="text" style="width: 360px" class="easyui-validatebox"  id="agentRemark" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("agentRemark") %>"/>
                </div>
            </div>

                <br>
            <div class="row">
                <div class="control-group span12">
                    <label class="control-label">网点所在区域:  </label>
                    <input type="text" style="width: auto" class="easyui-validatebox" id="channelAgentArea" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("channelAgentArea") %>"/>
                </div>
                <div class="control-group span12">
                    <label class="control-label">材料提交时间:  </label>
                    <input type="text" style="width: auto" class="easyui-validatebox" id="createDate" data-options="required:true" disabled="disabled" value="<%=request.getAttribute("createDate") %>"/>
                </div>
            </div>
            </fieldset>

            <fieldset class="span24">
                <legend>材料信息展示</legend>
                <a href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-search',plain:true" id="queryPicture">查看图片详情</a>
                <!-- 数据显示  -->
                <div>
                    <table class="easyui-datagrid" id="queryFileInfo" style="height:auto"
                           data-options="rownumbers:true,singleSelect:false,fitColumns:false,striped:true,loadMsg:'加载中...'">
                        <thead>
                        <tr>
                            <th data-options="field:'ck',checkbox:true"></th>
                            <th data-options="field:'fileName',align:'center',width:450">
                                材料名称
                            </th>
                            <th data-options="field:'fileType',align:'center',width:450">
                                材料类型
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </fieldset>

            <fieldset class="span24">
                <legend>材料处理信息</legend>
                <!-- 数据显示  -->
                <div>
                    <table class="easyui-datagrid" id="dealInfo" style="height:auto" title="处理信息（待办选项无处理信息）"
                           data-options="rownumbers:true,singleSelect:false,fitColumns:false,striped:true,loadMsg:'加载中...'">
                        <thead>
                        <tr>
                            <th data-options="field:'parentEntityName',align:'center',width:300">
                                所属分公司
                            </th>
                            <th data-options="field:'operId',align:'center',width:300">
                                操作账号
                            </th>
                            <th data-options="field:'doneDate',align:'center',width:300">
                                处理时间
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </fieldset>
        </form>
        <div class="row span24">
            <div class="row offset12">
                <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" id="closeWindow">关闭</a>
            </div>
        </div>
    </div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_export.js"></script>
<script>
    BUI.use("module/archive/agent/agentApplicationInfoQuery", function (agentApplicationInfoQuery) {
        new agentApplicationInfoQuery();
    });
</script>
</html>

