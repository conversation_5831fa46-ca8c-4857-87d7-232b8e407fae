<%--
  Created by IntelliJ IDEA.
  User: 靓仔
  Date: 2023/9/21
  Time: 16:26
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
    <title>股东认证异常回传信息查询</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
</head>
<body>
<!-- 容器开始 -->
<div class="container">
    <div class="row" style="padding:8px; height:auto">
        <div class="row">
            <form class="form-horizontal">
                <div class="row">
                    <div class="span12">
                        <label>接收集团回传文件时间：</label>
                        <input type="text" class="easyui-datebox" id="begDate" />~
                        <input type="text" class="easyui-datebox" id="endDate" />
                    </div>
                    <div class="span3">
                        <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- 容器结束 -->

<div class="container">
    <div id="result">
        <!-- 数据显示开始 -->
        <div class="row span24">
            <table id="shareholderAppInfoList" title="查询结果"
                   data-options="pageSize:10,height:350,width:1200,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
                <thead>
                <tr>
<%--                    <th data-options="field:'showTime',align:'center',width:150">接收集团回传文件时间</th>--%>
<%--                    <th data-options="field:'proCode',align:'center',width:100">省编码</th>--%>
                    <th data-options="field:'socIden',align:'center',width:150">统一社会信用代码</th>
                    <th data-options="field:'shareName',align:'center',width:120">股东姓名</th>
                    <th data-options="field:'docuType',align:'center',width:120" >证件类型</th>
                    <th data-options="field:'docuInfo',align:'center',width:180">证件号</th>
                    <th data-options="field:'isComHum',align:'center',width:100">是否我司员工</th>
                    <th data-options="field:'isComKinsman',align:'center',width:150">是否为我司员工直系亲属</th>
                    <th data-options="field:'kinEmployeeName',align:'center',width:150">亲属对应员工姓名</th>
                    <th data-options="field:'employeeId',align:'center',width:150">员工编号</th>
                    <th data-options="field:'isComMarketHum',align:'center',width:150">是否为我公司市场条线员工</th>
                    <th data-options="field:'isProHum',align:'center',width:120">是否为本省员工</th>
                    <th data-options="field:'referChannelEncoding',align:'center',width:200">涉及渠道全网渠道编码</th>
                    <th data-options="field:'channelName',align:'center',width:150">渠道名称</th>
                    <th data-options="field:'channelTrait',align:'center',width:150">渠道特征</th>
                    <th data-options="field:'exit1',align:'center',width:150">员工归属公司</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>



</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>

<script>
    BUI.use('module/archive/agent/shareholderAppInfo', function(
        shareholderAppInfo) {
        new shareholderAppInfo();
    });
</script>
</html>