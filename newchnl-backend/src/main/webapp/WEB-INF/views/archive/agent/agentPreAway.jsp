<!--
  
  $Id: agentPreAway.jsp,v 1.8 2014/08/30 10:05:50 fuqiang Exp $
  Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>终端网点添加</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
	.form-horizontal .controls {
		height: 30px;
	}
	
	.form-horizontal .control-label {
		height: 30px;
		width: 100px;
		line-height: 20px;
	}
	
	input{
		width: 100px;
	}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
	<div data-options="region:'west',split:true" border="false" title="组织树" style="width: 200px; padding: 10px;" id="actionTree"></div>
	<div region="center" title="" border="false" style="padding: 10px;">
		<form class="form-horizontal" id="ff" method="post">
			<input type="hidden" id="channelEntityId" name="channelEntityId"></input>
			<input type="hidden" id="operateType" name="operateType" value="PreAway"></input>
			<fieldset class="span18">
				<legend>基本信息</legend>
				<div class="row">
					<div class="control-group span6">
						<label class="control-label">代理商名称：</label>
						<div class="controls">
							<input type="text" id="channelEntityName" disabled="disabled" class="easyui-validatebox"  name="channelEntityName" />
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">代理商全称：</label>
						<div class="controls">
							<input type="text" disabled="disabled" name="fullName" class="easyui-validatebox"
								id="fullName" data-options="required:false"/>
						</div>
					</div>

					<div class="control-group span6">
						<label class="control-label">代理商等级：</label>
						<div class="controls">
							<select class="easyui-combobox" disabled="disabled"
								name="agentLevel"
								data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="agentLevel"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control_label span8">
						<label class="control-label">行业业态：</label>
						<div class="controls">
							<select class="easyui-combobox" disabled="disabled"
								name="industryType"
								data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
								id="industryType"></select>
						</div>
					</div>
				</div>
			</fieldset>
			
			<fieldset class="span18">
				<legend>企业信息</legend>
				<div class="row">
					<div class="control_label span6">
						<label class="control-label">企业性质：</label>
						<div class="controls">
							<select class="easyui-combobox" disabled="disabled"
								name="corporationCharacter"
								data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
								id="corporationCharacter"></select>
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">网点数：</label>
						<div class="controls">
							<input type="text" disabled="disabled" class="numberbox" class="easyui-validatebox"
								name="nodeNum" id="node_num"></input>
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">注册资金：</label>
						<div class="controls">
							<input type="text" disabled="disabled" name="corporationFund" class="easyui-validatebox"
								class="easyui-numberbox" id="corporation_fund"></input>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="control-group span6">
						<label class="control-label">企业法人：</label>
						<div class="controls">
							<input type="text" disabled="disabled" name="rel_relationName_2" class="easyui-validatebox"
								id="rel_relationName_2"></input>
						</div>
					</div>

					<div class="control-group span6">
						<label class="control-label">邮编：</label>
						<div class="controls">
							<input type="text" name="postCode" disabled="disabled" class="easyui-validatebox"
								id="post_code"></input>
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">归属组织：</label>
						<div class="controls">
							<select class="easyui-combobox" disabled="disabled" 
								name="districtId"
								data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
								id="districtId"></select>
						</div>
					</div>

				</div>
				<div class="row">
					<div class="control-group span6">
						<label class="control-label">行政区：</label>
						<div class="controls">
							<select class="easyui-combobox" disabled="disabled"
								name="regionId"
								data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
								id="regionId"></select>
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">地址：</label>
						<div class="controls">
							<input type="text" name="address" disabled="disabled" class="easyui-validatebox" id="address"></input>
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">工商号：</label>
						<div class="controls">
							<input type="text" name="businessNo" disabled="disabled" class="easyui-validatebox" id="businessNo"></input>
						</div>
					</div>
				</div>
			</fieldset>
			
			<fieldset class="span18">
				<legend>代理商状态</legend>
				<div class="row">
					<div class="control-group span6">
						<label class="control-label">代理商状态：</label>
						<div class="controls">
							<select class="easyui-combobox" disabled="disabled"
								name="channelEntityStatus"
								data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
								id="channelEntityStatus"></select>
						</div>
					</div>
				</div>
			</fieldset>

			<%--
			<fieldset class="span18">
				<legend>申请信息</legend>
				<div class="row">
					<div class="control-group span6">
						<label class="control-label">申请人：</label>
						<div class="controls">
							<input type="text" disabled="disabled" name="rel_relationName_3"
								id="rel_relationName_3"></input>
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">申请人联系方式：</label>
						<div class="controls">
							<input type="text" disabled="disabled" class="easyui-validatebox"
								data-options="required:true" name="rel_relationMobile_3"
								id="rel_relationMobile_3"></input>
						</div>
					</div>
					<div class="control-group span6">
						<label class="control-label">申请日期：</label>
						<div class="controls">
							<input type="text" class="easyui-datebox" name="rel_keyDate_3"
								disabled="disabled" class="easyui-datebox" id="rel_keyDate_3"
								class="easyui-datebox"></input>
						</div>
					</div>
				</div>
			</fieldset>
			--%>
		</form>
		<div class="row span18">
			<div id="add" align="center">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="saveBtn">提交</a>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/agent/agentPreAway',function(AgentPreAway){
		new AgentPreAway();
	});
</script>
</html>