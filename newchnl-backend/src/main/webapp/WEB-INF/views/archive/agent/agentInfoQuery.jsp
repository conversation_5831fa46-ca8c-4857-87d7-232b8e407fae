
<%--
 * 
 * $Id: agentInfoQuery.jsp,v 1.24 2014/09/26 11:03:58 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet"
	type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet"
	type="text/css" />
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<!-- 容器开始 -->

	<div class="container span24">
		<div class="row">
			<form class="form-horizontal">
				<div class="row">
					<div class="span6 control-group">
						<label class="control-label">合作方名称：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:false" id="full_or_entity_name" />
						</div>
					</div>
					<div class="span6 control-group">
						<label class="control-label">归属组织：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="district_id">
							</select>
						</div>
					</div>
					<div class="span6 control-group">
						<label class="control-label">行政区：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="region_id"></select>
						</div>
					</div>
						<div class="span6 control-group">
						<label class="control-label">合作方状态：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="channel_entity_status"></select>
						</div>
					</div>
				</div>
			</form>

			<div class="row  offset12" >
				<a href="javascript:void(0)" class="easyui-linkbutton" 
					data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
			</div>
		</div>

		<!-- operate 开始 -->
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search',plain:true" id="view">查看</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true" id="export">简易导出</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true" id="exportAll">全量导出</a>
			</div>
		</div>
		<!-- operate 结束 -->

		<!-- 数据显示开始 -->
		<div class="row">
			<table id="agentInfo" title="查询结果" class="span24"
				data-options="rownumbers:true,singleSelect:false"
				style="height: 440px">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'channelEntityId',hidden:'true',exportable:'false'">合作方ID</th>
						<th data-options="field:'fullName',width:'180',align:'center',exportable:'true'">合作方全称</th>
						<th data-options="field:'channelEntityName',width:'170',align:'center',exportable:'true'">合作方简称</th>
						<th data-options="field:'agentLevel',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10012',value);},exportable:'true'">合作方等级</th>
						<th data-options="field:'industryType',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10005',value);},exportable:'true'">行业业态</th>
						<th data-options="field:'corporationCharacter',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10006',value);},exportable:'true'">企业性质</th>
						<th data-options="field:'regionId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10023',value);},exportable:'true'">行政区</th>
						<th data-options="field:'districtId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10002',value);},exportable:'true'">归属组织</th>
						<th data-options="field:'channelEntityStatus',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10003',value);},exportable:'true'">合作方状态</th>
						<th data-options="field:'ext0',width:'100',align:'center', formatter: function(value,row,index){if(value == null || value == ''){return value;} else { return BUI.columnFormatter('50061',value);} },exportable:'true'">税率</th>
						<th data-options="field:'contract_number',width:'100',align:'center'">合同编号</th>
						<th data-options="field:'contract_mark_target',width:'100',align:'center'">合同名称</th>
						<th data-options="field:'contract_begin_date',width:'100',align:'center'">合同开始时间</th>
						<th data-options="field:'contract_end_date',width:'100',align:'center'">合同结束时间</th>
						<th data-options="field:'pass_old_month',width:'100',align:'center'">合同递延月份</th>
						<th data-options="field:'amount_including_tax',width:'100',align:'center'">合同含税总额</th>
						<th data-options="field:'amount_excluding_tax',width:'100',align:'center'">合同不含税总额</th>
						<th data-options="field:'vendor_code',width:'100',align:'center'">相对方编码</th>
						<th data-options="field:'vendor_name',width:'100',align:'center'">相对方名称</th>
						<th data-options="field:'trustees',width:'100',align:'center', formatter: function(value,row,index){if(value == null || value == ''){return value;} else {return BUI.columnFormatter('60001',value);}},exportable:'true'">直供代理方</th>
						<th data-options="field:'isIntraGroupTransaction',width:'100',align:'center', formatter: function(value,row,index){if(value == null || value == ''){return value;} else {return BUI.columnFormatter('60002',value);}},exportable:'true'">是否内部关联交易方</th>
					</tr>
				</thead>
			</table>
		</div>
		<!-- 数据显示结束 -->
	</div>
	<!-- 容器结束 -->





</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript"
	src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js"
	data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use('module/archive/agent/agentInfoQuery',function(AgentInfoQuery){
	new AgentInfoQuery();
});
</script>
</html>