
<%--
 * 
 * $Id: showAgentInfoView.jsp,v 1.10 2015/02/17 15:46:03 gongchao Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 * 代理商信息查看
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
<style type="text/css">
	.form-horizontal .control-label {
		width: 120px;
	}
	
	input[type="text"],input[type="password"],input[type="email"]{
		width: 150px;
		padding: 0px;
	}
	
	select{
		width: 150px;
		padding: 0px;
	}
</style>
</head>
<body>
	<div class="container">
		<form id="ff" class="form-horizontal" method="post">
	        <input id="channelEntityId" value="<%=request.getAttribute("channelEntityId") %>" type="hidden"/>
	        <input id="channel_entity_status" value="<%=request.getAttribute("channelEntityStatus") %>" type="hidden"/>
			<fieldset class="span24">
				<legend>基本信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">代理商名称:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" name="fullName" id="bean_full_name" data-options="required:true" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">代理商简称:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								name="channelEntityName" id="bean_channel_entity_name "
								data-options="required:true"  disabled="disabled"></input>
						</div>
					</div>
					

					<div class="control-group span8">
						<label class="control-label">代理商等级:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="agentLevel"
								data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="agentLevel"  disabled="disabled"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">签约时间:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" data-options="required:true"
								name="signBeginDate" id="signBeginDate" class="easyui-datebox"  disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">行业业态：</label>
						<div class="controls">
							<select class="easyui-combobox" name="industryType"
								data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'" id="industryType"  disabled="disabled"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">协议截止时间:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" data-options="required:true" name="signEndDate" id="signEndDate" class="easyui-datebox" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">是否托管:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input  type="text" class="easyui-combobox" name="istrusteeship"
									data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
									id="istrusteeship" disabled="disabled">
							</input>
						</div>
					</div>
					<div class="control-group span8" style="display: none;">
						<label class="control-label">直供代理方:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-combobox" name="trustees"
								   data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								   id="trustees" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">是否内部关联交易方:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input  type="text" class="easyui-combobox" name="isIntraGroupTransaction"
									data-options="required:true" disabled="disabled"
									id="isIntraGroupTransaction">
							</input>
						</div>
					</div>
				</div>
			</fieldset>

			<fieldset class="span24">
				<legend>企业信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">企业性质:  <span style="color:red;">*</span></label>
						<div class="controls">

							<select class="easyui-combobox" class="easyui-combobox"
								name="corporationCharacter"
								data-options="required:true,panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="corporationCharacter"  disabled="disabled"></select>

						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">网点数:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" name="nodeNum"
								id="bean_node_num" data-options="required:true"  disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">注册资金：</label>
						<div class="controls">
							<input type="text" class="easyui-numberbox"
								name="corporationFund"  id="bean_corporation_fund"  disabled="disabled"></input>
						</div>
					</div>
				</div>

				<div class="row">

					<div class="control-group span8">
						<label class="control-label">企业法人:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="rel_relationName_2"
								id="bean_relation_name"  disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">邮编:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easy-validatebox"
								data-options="required:true" name="postCode" id="bean_post_code"  disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">归属组织:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="districtId"
								data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="viewdistrictId" disabled="disabled"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">行政区:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="regionId"
								data-options="required:true,panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="viewregionId"  disabled="disabled"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">地址:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="address" id="bean_address"  disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">工商号:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="businessNo"
								id="bean_business_no"  disabled="disabled"></input>
						</div>
					</div>
				</div>
			</fieldset>


			<fieldset class="span24">
				<legend>代理商状态</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">代理商状态：</label>
						<div class="controls">
							<input type="text" name="channelEntityStatus" disabled="disabled"
								id="channelEntityStatus" value="申请"></input>
						</div>
					</div>
				</div>
			</fieldset>

			<!--合同信息-->
			<fieldset class="span24">
				<legend>合同信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">合同编号</label>
						<div class="controls">
								<input type="text" name="contract_number" id="contract_number" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">合同名称</label>
						<div class="controls">
							<input type="text" name="contract_mark_target" id="contract_Mark_Target" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">合同含税总额</label>
						<div class="controls">
							<input type="text" name="amount_including_tax" id="amount_including_tax" disabled="disabled"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">合同不含税总额</label>
						<div class="controls">
							<input type="text" name="amount_excluding_tax" id="amount_excluding_tax" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">合同开始</label>
						<div class="controls">
							<input type="text" name="contract_begin_date" id="contract_begin_date"  disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">合同结束</label>
						<div class="controls">
							<input type="text" name="contract_end_date" id="contract_end_date" disabled="disabled"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">相对方编码</label>
						<div class="controls">
							<input type="text" name="vendor_code" id="vendor_code" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">相对方名称</label>
						<div class="controls">
							<input type="text" name="vendor_name" id="vendor_name_c" disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">合同递延月</label>
						<div class="controls">
							<input type="text" name="pass_old_month" id="pass_old_month" disabled="disabled"></input>
						</div>
					</div>
				</div>
			</fieldset>
			<!-- 财务信息 -->
			<fieldset class="span24">
				<legend>财务信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">收取酬金银行(银行编码):  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="bank_bankCode_2"  disabled="disabled"
								id="bean_BANK_CODE_2"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">收取酬金银行支行:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="bank_bankBranch_2"  disabled="disabled"
								id="bean_BANK_BRANCH_2"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">收取酬金银行帐户名:</label>
						<div class="controls">
							<input type="text" data-options="required:true"
								class="easyui-validatebox" name="bank_accName_2"  disabled="disabled"
								id="bean_ACC_NAME_2"></input>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="control-group span8">
						<label class="control-label">收取酬金银行帐号:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" name="bank_bankAccount_2" disabled="disabled"
								id="bean_BANK_ACCOUNT_2"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">收取酬金户主身份证:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								data-options="required:true" name="bank_ext1_2" id="bean_EXT1_2"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">缴款银行(银行编码):  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								data-options="required:true" name="bank_bankCode_3" id="bean_BANK_CODE_3"></input>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="control-group span8">
						<label class="control-label">缴款银行支行:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								data-options="required:true" name="bank_bankBranch_3"
								id="bean_BANK_BRANCH_3"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">缴款银行帐户名:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								data-options="required:true" name="bank_accName_3"
								id="bean_ACC_NAME_3"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">缴款银行帐号:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								date-options="required:true" name="bank_bankAccount_3"
								id="bean_BANK_ACCOUNT_3"></input>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="control-group span8">
						<label class="control-label">缴款银行开卡绑定的手机号:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								date-options="required:true" data-options="required:true"
								name="bank_ext2_3" id="bean_EXT2_3"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">订单系统对应联系人姓名:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" date-options="required:true" disabled="disabled"
								name="rel_relationName_7" id="bean_RELATION_NAME_7"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">订单系统对应联系人电话:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								data-options="required:true" name="rel_relationMobile_7"
								id="bean_RELATION_MOBILE_7"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">订单系统对应联系人职位:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								data-options="required:true" name="rel_pos_7" id="bean_POS_7"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">担保人姓名或公司:</label>
						<div class="controls">
							<input type="text" name="rel_relationName_4" disabled="disabled"
								id="bean_RELATION_NAME_4"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">担保人职位:</label>
						<div class="controls">
							<input type="text" name="rel_pos_4" id="bean_POS_4"  disabled="disabled"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">缴款方式:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="paymentManner" disabled="disabled"
								data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="paymentManner"></select>

						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">纳税人类别:  <span style="color:red;">*</span></label>
						<div class="controls">

							<select class="easyui-combobox" name="taxpayerType" disabled="disabled"
								data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="taxpayerType"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">开票类别:  <span style="color:red;">*</span></label>
						<div class="controls">

							<select class="easyui-combobox" name="invoiceType" disabled="disabled"
								data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
								id="invoiceType"></select>

						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">税率登记:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="ext0"
									data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
									id="ext0" disabled="disabled"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">法人代表: </label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								   data-options="required:true" name="legalRepresentative"
								   id="legal_representative"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">统一社会信用代码:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								   data-options="required:true" name="socIden"
								   id="soc_iden"></input>
						</div>
					</div>
				</div>
			</fieldset>

			<!-- 业务联系人信息(选择输入) -->
			<fieldset class="span24">
				<legend>业务联系人信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">业务联系人:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"  disabled="disabled"
								data-options="required:true" name="rel_relationName_1"
								id="bean_RELATION_NAME_1"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">联系人电话:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								name="rel_relationMobile_1" data-options="required:true"
								id="bean_RELATION_MOBILE_1"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">其他联系方式:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" disabled="disabled"
								name="rel_otherContact_1" data-options="required:true"
								id="bean_OTHER_CONTACT_1"></input>
						</div>
					</div>
				</div>
				<div class="row">

					<div class="control-group span8">
						<label class="control-label">联系人邮箱：</label>
						<div class="controls">
							<input type="text" name="rel_email_1" id="bean_EMAIL_1"  disabled="disabled"></input>
						</div>
					</div>
				</div>
			</fieldset>
					<!-- 代理商服务号码 -->
			<fieldset class="span24">
				<legend>代理商服务号码<!--<input name="selectRelationManCheckbox" type="checkbox" value="on" id="selectBox"/> --> </legend>
				
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">用户名称:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								 name="rel_relationName_9"
								id="bean_relationName_9" data-options="required:true"  disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">服务指定号码1:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								name="rel_relationMobile_9" 
								id="bean_relationMobile_9" data-options="required:true,validType:'phone'"  disabled="disabled"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">服务指定号码2:</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								name="rel_otherContact_9" 
								id="bean_otherContact_9" data-options="validType:'phone'"  disabled="disabled"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">联系人地址:  </label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" name="rel_homeAddress_9" id="bean_homeAddress_9"  disabled="disabled"></input>
						</div>
					</div>
				</div>
				
			</fieldset>
			
			<!-- 代理商服务号码结束 -->

			<!--总部维护模块-->
			<fieldset class="span24">
				<legend>总部维护信息</legend>
				<!-- <div  style="display: none;" id="div_2"> -->
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">统一社会信用代码:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="uniftSocialCode" disabled="disabled"
								   id="uniftSocialCode"
								   class="easyui-validatebox" data-options="required:true"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">法人代表:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="legalPerson" disabled="disabled"
								   id="legalPerson" class="easyui-validatebox" data-options="required:true" ></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">法人代表证件类型:  <span style="color:red;">*</span></label>
						<div class="controls">
<%--							<select class="easyui-combobox" name="legalPersonType"--%>
<%--									data-options="required:true,panelHeight:'200',editable:false,valueField:'id',textField:'text'"--%>
<%--									id="legalPersonType"></select>--%>
							<select class="easyui-combobox" name="legalPersonType" disabled="disabled"
									data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text',formatter: function(value,row,index){return BUI.columnFormatter('10060',value);}"
									id="legalPersonType"></select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">法人代表证件号码:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="legalPersonInfo"  disabled="disabled" id="legalPersonInfo" data-options="required:true" class="easyui-validatebox"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">签约合作渠道商分类:  <span style="color:red;">*</span></label>
						<div class="controls">
							<select class="easyui-combobox" name="channelBusiness" disabled="disabled"
									data-options="required:true,panelHeight:'200',editable:false,valueField:'id',textField:'text'"
									id="channelBusiness"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">场景大类:  <span style="color:red;">*</span></label>
						<div class="controls">
							<div class="controls">
								<select type="text" class="easyui-validatebox" data-options="required:true,editable:false"  disabled="disabled" name="sceneCategory" id="sceneCategory"></select>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">场景子类:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="sceneSubclass"  disabled="disabled"
								   id="sceneSubclass" data-options="" class="easyui-validatebox"></input>
<%--							<select type="text" name="sceneSubclass"  disabled="disabled" id="sceneSubclass" data-options="required:true" class="easyui-validatebox"></select>--%>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">渠道商合作范围:  <span style="color:red;">*</span></label>
						<div class="controls">
							<input type="text" name="channelBusScope"  disabled="disabled"
								   id="channelBusScope" data-options="" class="easyui-validatebox"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">股东姓名:  <span style="color:red;"></span></label>
						<div class="controls">
							<input type="text" name="shareholderName"  disabled="disabled"
								   id="shareholderName" data-options="" class="easyui-validatebox"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">股东证件类型:  <span style="color:red;"></span></label>
						<div class="controls">
							<select class="easyui-combobox" name="shareholderType" disabled="disabled"
									data-options="required:true,panelHeight:'200',editable:false,valueField:'id',textField:'text'"
									id="shareholderType"></select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">股东证件号:  <span style="color:red;"></span></label>
						<div class="controls">
							<input type="text" name="shareholderInfo"  disabled="disabled"
								   id="shareholderInfo" data-options="" class="easyui-validatebox"></input>
						</div>
					</div>
				</div>
			</fieldset>
			<!--总部维护模块-->


			<!-- 客户经理信息(选择输入) -->
			<fieldset class="span24">
				<legend>客户经理信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">分管客户经理：</label>
						<div class="controls">
							<input type="text" name="rel_relationName_5"  disabled="disabled"
								id="bean_RELATION_NAME_5"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">客户经理电话：</label>
						<div class="controls">
							<input type="text" name="rel_relationMobile_5"  disabled="disabled"
								class="easyui-validatebox" data-options="required:true" id="bean_RELATION_MOBILE_5"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">客户经理邮箱：</label>
						<div class="controls">
							<input type="text" name="rel_email_5" id="bean_EMAIL_5"  disabled="disabled"
								class="easyui-validatebox"
								data-options="required:true,validType:'email'"></input>
						</div>
					</div>
				</div>
			</fieldset>
			<fieldset class="span24">
				<legend>其它信息</legend>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">是否短信领卡:</label>
						<div class="control">
							<select class="easyui-combobox" name="mes_get_card" disabled="disabled" data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'" id="mes_get_card">
								<Option value="1">是</Option>
								<option value="0">否</option>
							</select>
						</div>
					</div>
				</div>
			</fieldset>
		</form>
		<div class="row span24">
			<div class="row offset12">
				<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" id="closeWindow">关闭</a>
			</div>
		</div>
	</div>
	<!-- 查看窗口数据end -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/agent/showAgentInfoView',function(ShowAgentInfoView){
		new ShowAgentInfoView();
	});
</script>
</html>