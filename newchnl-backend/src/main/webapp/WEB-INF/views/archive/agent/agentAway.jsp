<%--
 * 
 * $Id: agentAway.jsp,v 1.6 2014/08/30 07:35:18 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
</head>
<body>
<!-- 容器开始 -->
	<div class="container">
		<div class="row" align="center" >
								<div >
									<label class="control-label">代理商名称或简称:</label>
										
												<input type="text" 
												          class="easyui-validatebox"
												          id="full_or_entity_name" data-options="required:false" />
										
								
					<div>
				    	 <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="queryBtn">查询</a>
				    </div>
				</div>
			
				
		<!-- 数据显示开始 -->
		<!-- operate 开始 -->
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="exitBtn">销户</a>
			</div>
		</div>
		<div class="row" >
			<table id="agentAwayInfo" title="查询结果" class="span24" data-options="rownumbers:true,singleSelect:true"  style="height: 440px">
			    <thead>
			        <tr>
			        	<th data-options="field:'ck',checkbox:true"></th>
			        	<th  data-options="field:'channelEntityId',hidden:'true'">代理商ID</th>
			        	<th data-options="field:'fullName',width:'240',align:'center'">代理商全称</th>
		    			<th data-options="field:'channelEntityName',width:'170',align:'center'">代理商简称</th>
		    			<!-- <th data-options="field:'agentLevel',width:'170',align:'center'">代理商等级</th>
		    			<th data-options="field:'industryType',width:'170',align:'center'">行业业态</th>
		    			<th data-options="field:'corporationCharacter',width:'170',align:'center'">企业性质</th>
		    			<th data-options="field:'regionId',width:'170',align:'center'">行政区</th>
		    			<th data-options="field:'districtId',width:'170',align:'center'">归属组织</th>
		    			<th data-options="field:'channel_entity_status',width:'170',align:'center'">代理商状态</th> -->
								    		
  						<th data-options="field:'agentLevel',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10012',value);}">代理商等级</th>
		    			<th data-options="field:'industryType',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10005',value);}">行业业态</th>
		    			<th data-options="field:'corporationCharacter',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10006',value);}">企业性质</th>
		    			<th data-options="field:'regionId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10023',value);}">行政区</th>
		    			<th data-options="field:'districtId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">归属组织</th>
		    			<th data-options="field:'channelEntityStatus',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10003',value);}">代理商状态</th> 
		    		
			        </tr>
			    </thead>
			    
			</table>
		</div>	
	
	</div>	

</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use('module/archive/agent/agentAway',function(AgentAway){
	new AgentAway();
});
</script>
</html>