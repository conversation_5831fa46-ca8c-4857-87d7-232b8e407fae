<%--
  Created by IntelliJ IDEA.
  User: dull
  Date: 2022/9/10
  Time: 14:11
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/common/header.jsp" %>
<html>
    <head>
        <title>合作申请信息展示</title>
        <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
        <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    </head>
    <body>
            <div class="control-group span24 row" align="center"><!-- 筛选条件 -->
                <form>
                    <div class="span10 control-group">
                        <label class="control-label">提交日期：</label>
                        <select  class="easyui-datebox span4"   id="createDate"> </select>
                    </div>

                    <div class="span10 control-group">
                        <label class="control-label">处理日期：</label>
                        <select class="easyui-datebox span4" id="dealDate"> </select>
                    </div>

                    <div class="control-group span10">
                        <label class="control-label">公司名称：</label>
                        <input id="channelEntityName"/>
                    </div>

                    <div class="control-group span10">
                        <label>列表选择：</label>
                        <select class="easyui-combobox span4"
                                data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
                                id="chooseList"></select>
                    </div>

                    <div class="control-group span4">
                        <a href="javascript:void(0)" class="easyui-linkbutton"
                           data-options="iconCls:'icon-search'" id="queryBtn">查询</a>
                    </div>
                </form>
            </div>

            <!-- operate 部分 -->
                <a href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-edit',plain:true" id="dealData">处理数据</a>
                <a href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-search',plain:true" id="queryTable">查看详情</a>
                <a href="javascript:void(0)" class="easyui-linkbutton"
                   data-options="iconCls:'icon-excel',plain:true" id="export">导出</a>
            <!-- operate 结束 -->

            <!-- 数据显示  -->
            <div class="row span24">
                    <table class="easyui-datagrid" id="queryResultInfo" style="height:auto" title="查询结果列表"
                           data-options="rownumbers:true,singleSelect:false,fitColumns:false,striped:true,loadMsg:'加载中...'">
                        <thead>
                            <tr>
                                <th data-options="field:'ck',checkbox:true"></th>
                                <th data-options="field:'agentId',align:'center',width:150">
                                    编号
                                </th>
                                <th data-options="field:'channelEntityName',align:'center',width:150">
                                    公司名称
                                </th>
                                <th data-options="field:'relationEmail' ,align:'center',width:200">
                                    联系人邮箱
                                </th>
                                <th data-options="field:'createDate' ,align:'center',width:170">
                                    提交时间
                                </th>
                                <th data-options="field:'dealDate' ,align:'center',width:170">
                                    处理时间
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>


    </body>
    <script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
    <script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
    <script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
    <script type="text/javascript" src="${ctx}/js/common/config.js"></script>
    <script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_export.js"></script>
    <script>
        BUI.use("module/archive/agent/agentApplicationInfoShow", function (agentApplicationInfoShow) {
            new agentApplicationInfoShow();
        });
    </script>
</html>
