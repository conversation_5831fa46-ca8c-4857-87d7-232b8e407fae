<%--
  Created by IntelliJ IDEA.
  User: 靓仔
  Date: 2023/8/24
  Time: 17:21
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
    <title>Test</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
</head>
<body>
<%--<div id="myLayout">--%>
<%--</div>--%>

<div class="container">
    <legend>信息录入区</legend>
    <form class="form-horizontal" enctype="multipart/form-data" id="partnerForm" >
        <div id="inputContainer">
            <div class="inputRow">
                    <label class="control-label">合作方：</label>
                    <input type="text" name="partner" id="partnerInfomation" required>
                <label >股东姓名：</label>
                <input type="text" name="name" id="shareName" >
                <label >证件类型：</label>
                <select id="documentType" name="documentType" >
                    <option value="身份证">身份证</option>
                    <option value="护照">护照</option>
                    <option value="军官证">军官证</option>
                    <option value="驾驶证">驾驶证</option>
                </select>
                <label for="documentNumber">证件号码：</label>
                <input type="text"  id="documentNumber" name="documentNumber" required>
                <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="addChild">添加</a>
                <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="deleteChild">删除</a>
            </div>
        </div>
    <div class="span8 offset11">
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">查询</a>
    </div>
    </form>
    <p id="errorMessage"></p>
</div>

<div class="container" >

    <legend>批量录入区</legend>

    <div class="row">
        <div class="span8 offset6">
            <label class="control-label">模板下载：</label>
            <a href="#" id="stockholderInfo">合作方股东信息模板</a>
        </div>
        <div>
            <label >文件编号：</label>
            <input type="text" name="name" id="batchId" >
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBatchBtn">查询</a>
        </div>
    </div>

    <div class="row">
        <div class="control-group span24" style="text-align: center;">
            <label class="control-label">导入文件：</label>
            <input type="file" data-options="required:true" style="width: 340px" id="fileinput" name="file"/>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="addBtn">批量录入</a>
        </div>
    </div>

</div>


<div class="container">
    <legend>结果展示区</legend>
    <div id="result">
        <div id="tb">
            <div>
                <div style="margin-bottom:8px">
                    <a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true"
                       id="exportBtn">结果导出</a>
                </div>
            </div>
        </div>

        <!-- 数据显示开始 -->
        <div class="row span24">
            <table id="detaNodeList" title="查询结果"
                   data-options="pageSize:10,height:350,width:1200,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
                <thead>
                <tr>
                    <th data-options="field:'ck',checkbox:true"></th>
                    <th data-options="field:'interfaceId',hidden:true">interfaceid</th>

                    <th data-options="field:'partnerInfomation',align:'center',width:150">
                        合作方信息
                    </th>
                    <th data-options="field:'shareName',align:'center',width:80">
                        股东姓名
                    </th>
                    <th data-options="field:'certificateInfo',align:'center',width:60">
                        证件类型
                    </th>
                    <th data-options="field:'documentInfo',align:'center',width:160">
                        证件信息
                    </th>
                    <th data-options="field:'errorMsg',align:'center',width:260">
                        接口异常信息
                    </th>
                    <th data-options="field:'isComHum',align:'center',width:80">
                        是否我司员工
                    </th>
                    <th data-options="field:'isComKinsman',align:'center',width:140">
                        是否我司员工直系亲属
                    </th>
                    <th data-options="field:'kinsmanName',align:'center',width:80">
                        亲属姓名
                    </th>
                    <th data-options="field:'employeeName',align:'center',width:80">
                        员工姓名
                    </th>
                    <th data-options="field:'employeeId',align:'center',width:120">
                        员工编号
                    </th>
                    <th data-options="field:'jonLine',align:'center',width:60">
                        职位条线
                    </th>
                    <th data-options="field:'deparType',align:'center',width:60">
                        部门类型
                    </th>
                    <th data-options="field:'isComMarketHum',align:'center',width:200">
                        是否为我公司市场条线员工
                    </th>
                    <th data-options="field:'exit1',align:'center',width:200">
                        员工归属公司
                    </th>
                </tr>
                </thead>
            </table>
        </div>



    </div>
</div>

</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>

<script>
    // $("#myLayout").css("height", document.documentElement.clientHeight);
    BUI.use('module/archive/agent/agentShareholderVeriftTest', function(
        agentShareholderVeriftTest) {
        new agentShareholderVeriftTest();
    });
</script>



</html>
