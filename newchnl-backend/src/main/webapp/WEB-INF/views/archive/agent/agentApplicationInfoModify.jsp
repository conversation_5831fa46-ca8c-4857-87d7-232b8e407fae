<%--
 * 
 * $Id: agentApplicationInfoModify.jsp,v 1.1 2014/09/24 05:31:04 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet"
	type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet"
	type="text/css" />
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<!-- 容器开始 -->

	<div class="container span24">
		<div class="row">
			<form class="form-horizontal">
				<div class="row">
					<div class="span8 control-group">
						<label class="control-label">代理商名称或简称：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:false" id="query_full_or_entity_name" />
						</div>
					</div>
					<div class="span8 control-group">
						<label class="control-label">归属组织：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
								id="query_district_id">
							</select>
						</div>
					</div>
					<div class="span8 control-group">
						<label class="control-label">行政区：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
								id="query_region_id"></select>
						</div>
					</div>
				</div>
			</form>
			<div class="row offset12">
				<a href="javascript:void(0)" class="easyui-linkbutton"
					data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
			</div>
		</div>
		<!-- operate 开始 -->
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true" id="edit">单条修改</a>
				<%--<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true" id="export">导出</a>--%>
			</div>
		</div>
		<!-- operate 结束 -->

		<!-- 数据显示开始 -->
		<div class="row">
			<table id="agentInfo" title="查询结果" class="span24" data-options="rownumbers:true,singleSelect:false" style="height: 440px">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'channelEntityId',hidden:'true'">代理商ID</th>
						<th data-options="field:'fullName',width:'240',align:'center'">代理商全称</th>
						<th data-options="field:'channelEntityName',width:'170',align:'center'">代理商简称</th>
						<th data-options="field:'agentLevel',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10012',value);}">代理商等级</th>
						<th data-options="field:'industryType',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10005',value);}">行业业态</th>
						<th data-options="field:'corporationCharacter',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10006',value);}">企业性质</th>
						<th data-options="field:'regionId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10023',value);}">行政区</th>
						<th data-options="field:'districtId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">归属组织</th>
						<th data-options="field:'channelEntityStatus',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10003',value);}">代理商状态</th>
						<th data-options="field:'paymentManner',hidden:'true',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10028',value);}">缴款方式</th>
						<th data-options="field:'taxpayerType',hidden:'true',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10049',value);}">纳税人类别</th>
						<th data-options="field:'invoiceType',hidden:'true',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10022',value);}">开票类别</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.validatebox.extends.js"></script>
<script type="text/javascript" defer="defer">
    window.onload=function() {
	BUI.use('module/archive/agent/agentApplicationInfoModify',function(AgentApplicationInfoModify) {
		new AgentApplicationInfoModify();
	});
    }
</script>
</html>