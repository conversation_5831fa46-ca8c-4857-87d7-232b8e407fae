<%--
 * 
 * $Id: agentImportEdit.jsp,v 1.11 2014/09/16 12:12:18 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet"
	type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet"
	type="text/css" />
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<!-- 容器开始 -->
	<div class="container">
		<div class="row">
			<form class="form-horizontal">
				<div class="row">
					<div class="span8 control-group">
						<label class="control-label">代理商名称或简称：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"data-options="required:false" id="full_or_entity_name" />
						</div>
					</div>

					<div class="span8 control-group">
						<label class="control-label">归属组织：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="district_id">
							</select>
						</div>
					</div>


					<div class="span8 control-group">
						<label class="control-label">行政区：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
								id="region_id"></select>
						</div>
					</div>
				</div>
			</form>

			<div class="row offset12">
				<a href="javascript:void(0)" class="easyui-linkbutton"
					data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
			</div>
		</div>

		<!-- operate 开始 -->
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton"data-options="iconCls:'icon-edit',plain:true" id="edit">单条修改</a>
				<a href="javascript:void(0)" class="easyui-linkbutton"data-options="iconCls:'icon-save',plain:true" id="export">导出</a>
			</div>
		</div>
		<!-- operate 结束 -->

		<!-- 数据显示开始 -->
		<div class="row">
			<table id="agentInfo" title="查询结果" class="span24"
				data-options="rownumbers:true,singleSelect:false"
				style="height: 440px">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'channelEntityId',hidden:'true'">代理商ID</th>
						<th data-options="field:'fullName',width:'240',align:'center'">代理商全称</th>
						<th
							data-options="field:'channelEntityName',width:'170',align:'center'">代理商简称</th>
						<th
							data-options="field:'agentLevel',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10012',value);}">代理商等级</th>
						<th
							data-options="field:'industryType',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10005',value);}">行业业态</th>
						<th
							data-options="field:'corporationCharacter',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10006',value);}">企业性质</th>
						<th
							data-options="field:'regionId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10023',value);}">行政区</th>
						<th
							data-options="field:'districtId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">归属组织</th>
						<th
							data-options="field:'channelEntityStatus',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10003',value);}">代理商状态</th>
						<th
							data-options="field:'paymentManner',hidden:'true',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10028',value);}">缴款方式</th>
						<th
							data-options="field:'taxpayerType',hidden:'true',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10049',value);}">纳税人类别</th>
						<th
							data-options="field:'invoiceType',hidden:'true',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10022',value);}">开票类别</th>
					</tr>
				</thead>
			</table>
		</div>
		<!-- 数据显示结束 -->
	</div>
	<!-- 容器结束 -->

	<!-- 查看窗口数据begin -->
	<div id="agentInfoViewWindow" class="easyui-window" title="代理商信息查看"
		style="height: 500px; width: 1200px;" data-options="closed:true,minimizable:false,maximizable:false,resizable:false">
		<!-- 查看窗口数据begin -->
		<form id="ff" class="form-horizontal" title="代理商信息查看" method="post">
			    <input type="hidden" name="channelEntityId" /> 
			    <input type="hidden" name="operateType" value="Upd" /> 
				<input type="hidden" name="channelEntityType" /> 
				<input type="hidden" name="channelEntitySerial" /> 
				<input type="hidden" name="parentEntity" />
				<input type="hidden" name="remark" /> 
				<input type="hidden" name="signBeginDate" /> 
				<input type="hidden" name="signEndDate" />
			<div class="container span8">
				<div class="row" style="padding: 8px; height: auto">
					<legend>基本信息</legend>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">代理商名称：</label>
							<div class="controls">
								<input type="text" disabled="disabled" name="channelEntityName" id="bean_channel_entity_name"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">代理商全称：</label>
							<div class="controls">
								<input type="text" disabled="disabled" name="fullName" id="bean_full_name"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">代理商等级：</label>
							<div class="controls">

								<select class="easyui-combobox" name="agentLevel" data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
									id="bean_agentLevel"></select>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">行业业态：</label>
							<div class="controls">

								<select class="easyui-combobox" name="industryType"
									data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
									id="bean_industry_type"></select>
							</div>
						</div>
					</div>
			
				</div>
		<div class="row" style="padding: 8px; height: auto">

					<legend>企业信息</legend>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">企业性质：</label>
							<div class="controls">

								<select class="easyui-combobox" name="corporationCharacter"data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"id="bean-corporationCharacter"></select>

							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">网点数:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"data-options="required:true" name="nodeNum" id="bean_node_num"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">注册资金:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"data-options="required:true" name="corporationFund"id="bean_corporation_fund"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">企业法人:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" name="rel_relationName_2"class="easyui-validatebox" data-options="required:true" id="bean_relation_name"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">邮编:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" name="postCode" class="easyui-validatebox" data-options="required:true" id="bean_post_code"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">归属组织：</label>
							<div class="controls">

								<select class="easyui-combobox" name="districtId" data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'" id="bean_district_id"></select>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="control-group span8">
							<label class="control-label">行政区：</label>
							<div class="controls">

								<select class="easyui-combobox" name="regionId"data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"id="bean_region_id"></select>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">地址:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"data-options="required:true" name="address" id="bean_address"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">工商号:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" name="businessNo" class="easyui-validatebox"data-options="required:true" id="bean_business_no"></input>
							</div>
						</div>
					</div>

				</div>


				<div class="row" style="padding: 8px; height: auto">

					<legend>代理商状态*</legend>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">代理商状态：</label>
							<div class="controls">
								<select class="easyui-combobox" disabled="disabled"name="channelEntityStatus"data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"id="bean_channel_entity_status"></select>
							</div>
						</div>

					</div>

				</div>

				</div>


				<!-- 财务信息 -->
				<div class="row" style="padding: 8px; height: auto">

					<legend>财务信息</legend>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">收取酬金银行(银行编码):  <span style="color:red;">*</span></label>
							<div class="controls">
								<select type="text" class="easyui-combobox"
								data-options="required:true" name="bank_bankCode_2"id="bean_BANK_CODE_2"></select>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">收取酬金银行支行:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"data-options="required:true" name="bank_bankBranch_2"id="bean_BANK_BRANCH_2"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">收取酬金银行帐户名:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
									data-options="required:true" name="bank_accName_2"
									id="bean_ACC_NAME_2"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">收取酬金银行帐号:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"data-options="required:true" name="bank_bankAccount_2" id="bean_BANK_ACCOUNT_2"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">收取酬金银行户主身份证:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"  name="bank_ext1_2" id="bean_EXT1_2" data-options="required:true"></input>
								</di>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">缴款银行支行:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"data-options="required:true" name="bank_bankBranch_3"id="bean_BANK_BRANCH_3"></input>
							</div>
						</div>
					</div>

					<div class="control-group span8">
							<label class="control-label">缴款银行(银行编码)*：</label>
							<div class="controls">
								<select class="easyui-validatebox" data-options="required:false" name="bank_bankCode_3" id="bean_BANK_CODE_3"></select>
							</div>
						</div>
						

					<div class="row">

						<div class="control-group span8">
							<label class="control-label">缴款银行帐户名:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" name="bank_accName_3"id="bean_ACC_NAME_3"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">缴款银行帐号:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" name="bank_bankAccount_3"id="bean_BANK_ACCOUNT_3"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">缴款银行开卡绑定的手机号:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" name="bank_ext2_3" id="bean_EXT2_3"></input>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="control-group span8">
							<label class="control-label">订单系统联系人姓名:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
									data-options="required:true" name="rel_relationName_7"
									id="bean_RELATION_NAME_7"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">订单系统联系人电话:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
									data-options="required:true" name="rel_relationMobile_7"
									id="bean_RELATION_MOBILE_7"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">订单系统联系人职位:  <span style="color:red;">*</span></label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
									data-options="required:true" name="rel_pos_7" id="bean_pos_7"></input>
							</div>
						</div>
					</div>
					<div class="row">

						<div class="control-group span8">
							<label class="control-label">担保人姓名或公司：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
									data-options="required:false" name="rel_relationName_4"
									id="bean_RELATION_NAME_4"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">担保人职位：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
									data-options="required:false" name="rel_pos_4" id="bean_POS_4"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">缴款方式:  <span style="color:red;">*</span></label>
							<div class="controls">
								<select class="easyui-combobox" class="easyui-validatebox"
									name="paymentManner"
									data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
									id="bean_payment_manner"></select>

							</div>
						</div>
					</div>

					<div class="row">
						<div class="control-group span8">
							<label class="control-label">纳税人类别：</label>
							<div class="controls">
								<select class="easyui-combobox" class="easyui-combobox" name="taxpayerType"
									data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
									id="bean_taxpayer_type"></select>
							</div>
						</div>

						<div class="control-group span8">
							<label class="control-label">开票类别：</label>
							<div class="controls">
								<select class="easyui-combobox" class="easyui-combobox" name="invoiceType"
									data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
									id="bean_invoice_type"></select>

							</div>
						</div>

					</div>

				</div>

				<!-- 业务联系人信息(选择输入) -->
				<div class="row" style="padding: 8px; height: auto">
					<legend>业务联系人信息</legend>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">业务联系人：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:false"name="rel_relationName_1" id="bean_RELATION_NAME_1"></input>
							</div>
						</div>

						<div class="control-group span8">
							<label class="control-label">联系人电话：</label>
							<div class="controls">
								<input type="text"	class="easyui-validatebox" data-options="required:false"name="rel_relationMobile_1" id="bean_RELATION_MOBILE_1"></input>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">其他联系方式：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:false"name="rel_otherContact_1" id="bean_OTHER_CONTACT_1"></input>
							</div>
						</div>
					</div>
					<div class="row">

						<div class="control-group span8">
							<label class="control-label">联系人邮箱：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:false" name="rel_email_1" id="bean_EMAIL_1"></input>
							</div>
						</div>
					</div>

				</div>
				<!-- 客户经理信息(选择输入) -->
				<div class="row" style="padding: 8px; height: auto">

					<legend>业务联系人信息</legend>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">分管客户经理：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" name="rel_relationName_5" data-options="required:false" id="bean_RELATION_NAME_5"></input>
							</div>
						</div>

						<div class="control-group span8">
							<label class="control-label">客户经理电话：</label>
							<div class="controls">
								<input type="text" 	data-options="required:false" class="easyui-validatebox" name="rel_relationMobile_5" id="bean_RELATION_MOBILE_5"></input>
							</div>
						</div>
						<div class="control-group span8">

							<label class="control-label">客户经理邮箱：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" name="rel_email_5" id="bean_EMAIL_5" data-options="required:false,validType:'email'"></input>
							</div>
						</div>
					</div>

				</div>
				<div class="row">
					<div class="row offset6">
						<a id="save" href="#" class="easyui-linkbutton"
							data-options="iconCls:'icon-save'">提交</a> <a
							href="javascript:void(0)" class="easyui-linkbutton"
							iconCls="icon-search" id="closeWindow">关闭</a>
					</div>
				</div>
		</form>
		<!-- 查看窗口数据end -->
	</div>
	<!-- 查看窗口数据end -->



</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript"
	src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
	<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js"
	data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript"
	src="${ctx}/js/common/jquery.validatebox.extends.js"></script>
<script>
	BUI.use('module/archive/agent/agentImportEdit', function(AgentImportEdit) {
		new AgentImportEdit();
	});
</script>
</html>