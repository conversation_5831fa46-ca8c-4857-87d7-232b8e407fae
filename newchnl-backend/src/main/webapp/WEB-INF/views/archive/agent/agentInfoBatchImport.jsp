<!--
  
  $Id: agentInfoBatchImport.jsp,v 1.4 2014/10/22 08:14:28 linfeng Exp $
  Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>代理商批量录入</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
	.form-horizontal .controls {
		height: 30px;
	}
	
	.form-horizontal .control-label {
		height: 30px;
		width: 100px;
		line-height: 20px;
	}
	
	input{
		width: 100px;
	}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
	<div data-options="region:'west',split:true" border="false" title="组织树" style="width: 200px; padding: 10px;" id="actionTree"></div>
	<div region="center" title="" border="false" style="padding: 10px;">
		<form class="form-horizontal" id="ff" method="post">
		<input 	name="parentEntity"	id="parentEntity" type="hidden"></input>
			<fieldset class="span18">
				<legend>代理商批量录入</legend>
				<div class="row">
					<div class="control-group span20">
						<label class="control-label">归属分公司<span style="color:red;">*</span>：</label>
						<div class="controls">
							<input class="easyui-validatebox" disabled="disabled"
								name="parentEntityName"	id="parentEntityName" ></input>
						</div>
					</div>
				</div>
				
				<div class="row">
					<div class="control-group span20">
						<label class="control-label">模板下载：</label>
						<div class="controls">
							<a href="#" id="agentTemplet">合作方批量例如模板.xls</a>
						</div>
					</div>
				</div>
				
				
				<div class="row">
					   <div class="control-group span20">
						        <label class="control-label">导入文件<span style="color:red;">*</span>：</label>
						   		<input type="file"  data-options="required:true" style="width: 340px"  id="fileinput" name="file"  />
						</div>
					</div>
			</fieldset>

			
		</form>
		<div class="row span18">
			<div  align="center">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="import">导入</a>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/agent/agentInfoBatchImport',function(AgentInfoBatchImport){
		new AgentInfoBatchImport();
	});
</script>
</html>