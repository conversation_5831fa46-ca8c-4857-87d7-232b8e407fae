<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@include file="/common/header.jsp" %>
<html>
<head>
    <title>直随销渠道黑名单管理</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
</head>
<body>
<div class="container span24">
    <div class="row">
        <form class="form-horizontal">
            <div class="row">
                <div class="span8 control-group">
                    <label class="control-label">身份证号：</label>
                    <div class="controls">
                        <input type="text" class="easyui-validatebox"
                               data-options="required:false" id="id_card" />
                    </div>
                </div>
                <div class="row  offset12" >
                    <a href="javascript:void(0)" class="easyui-linkbutton"
                       data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
                </div>
            </div>
        </form>


    </div>

    <!-- operate 开始 -->
    <div id="tb">
        <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="deleteBtn">删除</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="queryDeleteBtn">删除操作查看</a>

    </div>

    <!--数据回显-->
    <div class="row" style="margin-top: 10px">
        <table id="directBlacklistInfo" title="查询结果" class="span24"
               data-options="rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'"
               style="height: 400px;width: 1100px;">
            <thead>
            <tr>
                <th data-options="field:'ck',checkbox:true"></th>
                <th data-options="field:'name',width:'200',align:'center',exportable:'true'">姓名</th>
                <th data-options="field:'idCard',width:'180',align:'center',exportable:'true'">身份证号</th>
                <th data-options="field:'companyName',width:'180',align:'center',exportable:'true'">归属分公司</th>
                <th data-options="field:'reason',width:'180',align:'center',exportable:'true'">入黑名单原因</th>
                <th data-options="field:'createDate',width:'180',align:'center',exportable:'true'">录入日期</th>
                <th data-options="field:'opName',width:'180',align:'center',exportable:'true'">录入人</th>
            </tr>
            </thead>
        </table>
    </div>
</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
    var _orgId = "<%= request.getAttribute("orgId") %>";
    var _orgName = "<%= request.getAttribute("orgName") %>";
    BUI.use('module/archive/agent/directBlacklistInfoManager',function(DirectBlacklistInfoManager){
        new DirectBlacklistInfoManager();
    });
</script>
</html>
