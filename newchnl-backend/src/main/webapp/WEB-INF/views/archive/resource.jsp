<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>资源信息管理</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<div class="container span24">
		<div class="row" style="padding:8px; height:auto">
			<form class="form-horizontal">
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">资源名称：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" id="resourceName"></input>
						</div>
					</div>
					<div class="span8 offset11">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">搜索</a>
					</div>
				</div>
			</form>
		</div>

		<div id="tb" style="padding:8px; height:auto">
			<div style="margin-bottom:8px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="addBtn">添加</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true" id="editBtn">修改</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="deleteBtn">删除</a>
			</div>
		</div>
		<div class="row">
			<table id="commerceCircleTable" title="资源信息" class="span24" style="height:400px"
				data-options="rownumbers:true,singleSelect:true">
			    <thead>
			        <tr>
			        	<th data-options="field:'ck',checkbox:true"></th>
		    			<th data-options="field:'resourceId'">资源ID</th>
		    			<th data-options="field:'resourceName'">资源名称</th>
		    			<th data-options="field:'resourceUrl'">资源URL</th>    		
			        </tr>
			    </thead>
			</table>
		</div>
	</div>
	
	
	<div id="commerceCircleWindow" class="easyui-window" title="资源信息编辑" style="height:400px;width:600px;" 
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div class="container span8" >
			<div class="row" style="padding:8px; height:auto">
				<form class="form-horizontal">
					<input type="hidden" id="resource_id"></input>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">资源名称：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="resource_name"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">资源URL：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="resource_url"></input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="span8 offset6">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="saveBtn">保存</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="cancleBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</body>

<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('../js/module/archive/resourceInfo',function(ResourceInfo){
		new ResourceInfo();
	});
</script>
</html>