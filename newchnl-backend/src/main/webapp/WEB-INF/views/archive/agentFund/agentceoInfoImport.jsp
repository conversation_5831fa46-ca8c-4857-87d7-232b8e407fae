
<%--代理商各分公司保证金负责人录入
 * 
 * $Id: agentceoInfoImport.jsp,v 1.9 2014/11/19 13:31:01 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>录入</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet"
	type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet"
	type="text/css" />
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
<link rel="stylesheet" type="text/css"href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
<style type="text/css">
.form-horizontal .controls {
	height: 30px;
}

.form-horizontal .control-label {
	height: 30px;
	width: 100px;
	line-height: 20px;
}

input[type="text"],input[type="password"],input[type="email"] {
	width: 100px;
}
</style>
</head>
<body class="easyui-layout" style="padding: 10px;">

	<!-- 负责人录入 -->
	<div region="center" title="" border="false" style="padding: 10px;">
		<div class="row">
			<form class="form-horizontal">
				代理商各分公司保证金负责人录入
				<hr />
				<div class="row">
					<div class="control-group span6">
						<label class="control-label">分公司：</label>
						<div class="controls">
							<select class="easyui-combobox" id="regoryCompany"
								data-options="panelHeight:'auto', required:false,editable:false,
									valueField:'id',textField:'text'">
							</select>
						</div>
					</div>

					<div class="control-group span6">
						<label class="control-label">负责人：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true" id="name_like"></input>
						</div>

					</div>

					<div class="control-group span6">
						<label class="control-label">联系电话：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"
								data-options="required:true,validType:'phone'" id="telphone"></input>
						</div>

					</div>

				</div>
				<div class="row">
					<div class="span8 offset8" style="margintop:10px;margin-bottom:10px">
						<a href="javascript:void(0)" class="easyui-linkbutton"
							iconCls="icon-save" id="insertBtn">录入</a>
					</div>
				</div>
			</form>
		</div>
		<div id="tb">
			<div style="margin-bottom: 8px">

				<a href="javascript:void(0)" class="easyui-linkbutton"
					data-options="iconCls:'icon-edit',plain:true" id="view">编辑</a>
			</div>
		</div>
		<!-- 数据显示开始 -->
		<div class="row">
			<table id="agentFundCeoList" class="easyui-datagrid span28"
				style="width: 710px;"
				data-options="pageSize:10,height:380,pagination:true,striped:true,toolbar:'#tb',loadMsg:'加载中...'">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th
							data-options="field:'regorgId',width:'240',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('50445',value);}">分公司</th>
						<th data-options="field:'nameLink',align:'center',width:'240'">联系人</th>
						<th data-options="field:'tel',width:'270',align:'center'">联系电话</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
	<!-- 数据显示结束 -->
	<!-- 各分公司负责人信息修改 -->

	<div id="agentDepositLikeManWindow" class="easyui-window"
		title="各分公司负责人信息修改"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			    minimizable:false,maximizable:false,resizable:false">
		<div class="container">
			<div class="row" style="padding: 8px; height: auto" id="depositlinkManForm">
				<form class="form-horizontal" >
					<!-- <input type="hidden" id="central_id"></input> -->
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">分公司<s>*</s>:</label>
							<div class="controls">
								<select type="checkbox" class="easyui-combobox"
									id="codeNameCompanyEdit"
									disabled="disabled"
									data-options="panelHeight:'auto',
									required:true,
									editable:false,
									valueField:'id',
									textField:'text'">
								</select>
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">联系人:</label>
							<div class="controls">
								<input type="text" id="bean_relike_man" class="easyui-validatebox" data-options="required:false"></input>
							</div>

						</div>
					</div>
					
					
						<div class="row">
							<div class="control-group span8">
								<label class="control-label">电话:</label>
								<div class="controls">
									<input type="text" id="bean_tel"class="easyui-validatebox" data-options="required:false,validType:'phone'"></input>
								</div>

							</div>
						</div>

						<div class="row">
							<div class="offset7">
								<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-save" id="update_savesBtn">确定</a> <a
									href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-no" id="update_cancleBtn">取消</a>
							</div>
						</div>
				</form>
			</div>
		</div>
	</div>

</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript"src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript"src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript"src="${ctx}/js/common/jquery.validatebox.extends.js"></script>

<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<!-- 年月日期控件 -->
<script type="text/javascript"src="${ctx}/js/module/archive/channelSupportOffce/yearMonthTime.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js"data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/agentFund/agentceoInfoImport', function(
			AgentceoInfoImport) {
		new AgentceoInfoImport();
	});
</script>
</html>