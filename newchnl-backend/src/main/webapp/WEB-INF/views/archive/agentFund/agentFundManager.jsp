<%--
 *
 * $Id: agentFundManager.jsp,v 1.10 2015/03/14 17:34:23 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
	<title>保证金维护</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
	<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
		.form-horizontal .controls {
			height: 30px;
		}

		.form-horizontal .control-label {
			height: 30px;
			width: 100px;
			line-height: 20px;
		}

		input[type="text"],input[type="password"],input[type="email"] {
			width: 100px;
		}
	</style>
</head>

<body class="easyui-layout" style="padding: 10px;">
<div data-options="region:'west',split:true" border="false" title="组织树"style="width: 200px; padding: 10px;" id="actionTree"></div>
<!-- 容器开始 -->
<div region="center" title="" border="false" style="padding: 10px;">
	<div class="row">
		<form class="form-horizontal">
			保证金维护
			<hr />
			<!-- 列开始 -->
			<div class="row">
				<div class="span8 control-group">
					<label class="control-label">账号：</label>
					<div class="controls">
						<select class="easyui-combobox" id="acc_id"
								data-options="panelHeight:'auto', required:false, editable:false,valueField:'id',textField:'text'">
							<option value="">--没有账号--</option>
						</select>
					</div>
				</div>

				<div class="span8 control-group">
					<label class="control-label">科目：</label>
					<div class="controls">
						<input class="easyui-combobox" id="acc_name"data-options="panelHeight:'auto', required:false,editable:false,valueField:'id',textField:'text'">
						</input>
					</div>
				</div>

			</div>
			<!-- 列结束-->
			<!-- 列开始 -->

			<div class="row">
				<div class="span8 offset6">
					<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-search" id="queryBtn">查询</a>
				</div>
			</div>
			<!-- 列结束-->
		</form>
	</div>

	<!-- 各个按钮 -->
	<!-- 各个按钮 -->
	<div id="tb">
		<div style="margin-bottom: 8px">
			<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-view',plain:true"  id="agentFundHistory">明细</a>
			<!-- <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"id="agentFundInputBtn">存入</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-reload',plain:true"id="agentFundOutputBtn">支取</a>

            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true" id="editBtn">修正</a> -->
		</div>
	</div>
	<!-- 按钮结束 -->

	<!-- 数据显示开始 -->
	<div class="row">
		<table id="agentFundDetailList" class="easyui-datagrid span24"
			   style="width: 700px;"
			   data-options="pageSize:10,height:380,pagination:true,striped:true,loadMsg:'加载中...'">
			<thead>
			<tr>
				<th data-options="field:'ck',checkbox:true"></th>
				<th data-options="field:'accId',align:'center',width:'100'">账号</th>
				<th data-options="field:'accName',align:'center',width:'100'">科目</th>
				<th data-options="field:'validDate',align:'center',width:150">生效日期</th>
				<th data-options="field:'nodeName',align:'center',width:200">网点名称</th>
				<th data-options="field:'totalFee',align:'center',width:100,formatter: function(value,row,index){return BUI.columnFormatterMoneny(value);}">金额(元)</th>
			</tr>
			</thead>
		</table>
		<!-- 数据显示结束 -->
	</div>
</div>
<!-- 弹出层  保证金存入div -->
<div id="agentFundManagerViewInWindow" class="easyui-window"title="保证金存入"data-options="modal:true,closed:true,iconCls:'icon-edit',minimizable:false,maximizable:false,resizable:false">
	<div class="container">
		<div class="row" style="padding: 8px; height: auto">
			<form class="form-horizontal" id="agentFundManagerViewInWindowForm">
				<input type="hidden" id="central_id"></input> <input type="hidden"
																	 id="fundInAccCode" />
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">帐号<span style="color: red;">*</span>：
						</label>
						<div class="controls">

							<input type="text" class="easyui-validatebox" data-options="required:true" id="accFundInId" disabled="disabled"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">科目<span style="color: red;">*</span>：
						</label>
						<div class="controls">
							<input type="text" class="easyui-combobox" id="accFundInName"disabled="disabled"data-options="panelHeight:'auto',required:false,editable:true,valueField:'id',textField:'text'"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">金额(元)<span
								style="color: red;">*</span>：
						</label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" precision="2" data-options="required:false" id="totalFundInFee"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">备注:</label>
						<textarea rows="10" cols="10" id="FundInnotes"></textarea>
					</div>
				</div>
				<div class="row">
					<div class="offset3">
						<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-save" id="btn_saveFundIn">确定</a>
						<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-no" id="btn_closeFundIn">取消</a>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
<!-- 保证金存入结束 -->

<!-- 保证金提取begin-->
<div id="agentFundOutViewWindow" class="easyui-window" title="保证金提取"data-options="modal:true,closed:true,iconCls:'icon-edit',minimizable:false,maximizable:false,resizable:false">
	<div class="container">
		<div class="row" style="padding: 8px; height: auto">
			<!-- 查看窗口数据begin -->
			<form class="form-horizontal" id="agentFundOutViewWindowForm">
				<input id="agentFundOutChannelEntityStatus" type="hidden" />
				<input id="agentFundOwnerNumber" type="hidden" />
				<input id="fundOutAccCode" type="hidden" />
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">账号<span style="color: red;">*</span>：
						</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" id="accFundOutId"data-options="required:true"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">选择科目<span
								style="color: red;">*</span>：
						</label>
						<div class="controls">
							<input type="text" class="easyui-combobox" id="fundOutAccName"data-options="panelHeight:'auto',required:false,editable:false,valueField:'id',textField:'text'"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">金额(元)<span style="color: red;">*</span>：
						</label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" precision="2"  data-options="required:true" id="fundOutTotalFee"></input>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="control-group span8">
						<label class="control-label">备注：</label>
						<textarea rows="10" cols="10" id="fundOutNotes"></textarea>
					</div>
				</div>

				<div class="row">
					<div class="offset3">
						<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-save" id="saveFundOut">确定</a>
						<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-no" id="closeFundOutWindow">取消</a>
					</div>
				</div>
			</form>
		</div>
	</div>

</div>
<!-- 保证金管理明细 -->

<!-- 明细  -->
<div id="agentBusiFunctionDtlParticularWindow" class="easyui-window"
	 title="保证金历史记录查看"
	 data-options="modal:true,closed:true,iconCls:'icon-edit',minimizable:false,maximizable:false,resizable:false">
	<div class="container">
		<table id="agentFundQueryParticularTable"class="easyui-datagrid span24" title="正数的金额为存入的保证金，负数的金额为支出的保证金"data-options="pageSize:10,height:300,pagination:true,width:600,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
			<thead>
			<tr>
				<th data-options="field:'accId',align:'center',width:100">帐号</th>
				<th data-options="field:'accName',align:'center',width:100">科目</th>
				<th data-options="field:'busiCode1',align:'center',width:100">业务名称</th>
				<!-- <th data-options="field:'totalFee',align:'center',width:100">金额(元)</th> -->
				<th data-options="field:'totalFeeStr',align:'center'">金额(元)</th>
				<th data-options="field:'doneDate',align:'center',width:120">操作日期</th>
				<th data-options="field:'notes',align:'center',width:150">备注</th>
			</tr>
			</thead>
		</table>
	</div>
	<div class="row">
		<div class="offset3">
			<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-excel" id="particular_XlssavesBtn">导出Excel</a>
			<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-no" id="particular_XlscancleBtn">后退</a>
		</div>
	</div>
</div>
<!-- 明细弹出层结束 -->
<!-- 修正开始 -->

<div id="agentBusiFunctionDtlUpdateWindow" class="easyui-window"title="保证金修正"data-options="modal:true,closed:true,iconCls:'icon-edit',minimizable:false,maximizable:false,resizable:false">
	<div class="container">
		<div class="row" style="padding: 8px; height: auto">
			<form class="form-horizontal" id="agentBusiFunctionDtlUpdateWindowId">
				<input type="hidden" id="central_id"></input>
				<input type="hidden"id="editAccCode" />
				<input type="hidden" id="agentOwerFunds"/>

				<div class="row">
					<div class="control-group span8">
						<label class="control-label">帐号<span style="color: red;">*</span>：
						</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox"data-options="required:true" id="update_accId" disabled="disabled"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">科目<span style="color: red;">*</span>：
						</label>
						<div class="controls">
							<input type="text" class="easyui-combobox" id="update_accName"disabled="disabled"data-options="panelHeight:'auto',required:true,editable:false,valueField:'id',textField:'text'"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">保证金类型<span
								style="color: red;">*</span>：
						</label>
						<div class="controls">
							<input type="text" class="easyui-combobox"id="update_totalFeeJuede"data-options="panelHeight:'auto',required:true,editable:false,valueField:'id',textField:'text'"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">金额(元)<span style="color: red;">*</span>：
						</label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" precision="2" data-options="required:true" id="update_totalFee"></input>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">备注：</label>
						<textarea rows="10" cols="10" id="update_notes"></textarea>
					</div>
				</div>
				<div class="row">
					<div class="offset3">
						<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-save" id="update_savesBtn">确定</a>
						<a href="javascript:void(0)" class="easyui-linkbutton"iconCls="icon-no" id="update_cancleBtn">取消</a>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js"data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script>
    BUI.use('module/archive/agentFund/agentFundManager', function(AgentFundManager) {
        new AgentFundManager();
    });
</script>
</html>