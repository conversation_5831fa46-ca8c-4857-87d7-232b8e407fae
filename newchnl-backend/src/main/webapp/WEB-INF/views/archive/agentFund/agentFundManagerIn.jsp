
<%--保证金录入页面
 * 
 * $Id: agentFundManagerIn.jsp,v 1.4 2014/11/19 13:31:01 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
	<title>保证金输入页面</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
	<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
	<style type="text/css">
		.form-horizontal .controls {
			height: 30px;
		}
		.form-horizontal .control-label {
			height: 30px;
			width: 90px;
			line-height: 10px;
		}

		input[type="text"], input[type="password"], input[type="email"]{
			width:100px;
		}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
<div data-options="region:'west',split:true"  border="false" title="组织树" style="width:180px; padding: 10px;" id="actionTree">
</div>
<!-- 录入 -->
<div region="center" title="" border="false" style="padding: 10px;">
	<form class="form-horizontal" id="ff" method="post">
		<input type="hidden" id="regionid" />
		保证金录入
		<hr />
		<input name="agentId" id="agentId" type="hidden"/>

		<div class="row">
			<div class="control-group span6">
				<label class="control-label">帐号<span style="color:red;">*</span>：</label>
				<div class="controls">
					<input type="text" class="easyui-validatebox" data-options="required:true" id="accId" readonly="readonly"></input>
				</div>
			</div>
			<div class="control-group span6">
				<label class="control-label">选择科目<span style="color:red;">*</span>：</label>
				<div class="controls">
					<input type="text" class="easyui-combobox"  id="accName"
						   data-options="panelHeight:'200',
									required:true,
										editable:false,
										valueField:'id',
										textField:'text'">
					</input>
				</div>
			</div>
			<div class="control-group span6" id="business" style="display: none">
				<div class="controls">
					<label>
						<span id="titleName">选择泛渠道网点</span>
						<span style="color:red">*</span>：
					</label>
				</div>
				<div class="controls">
					<input type="text" class="easyui-combobox"  id="nodeId"
						   data-options="panelHeight:'auto',
										required:true,
										editable:false,
										width:100,
										valueField:'id',
										textField:'text'"/>
				</div>
			</div>
			<div id="sp">
				<div style="margin: 2px;">
					</span> <span> <a id="QueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">请选择</a></span>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="control-group span6">
				<label class="control-label">存入金额(元)<span style="color:red;">*</span>：</label>
				<div class="controls">
					<input type="text" class="easyui-numberbox" data-options="required:true" precision="2"  id="totalFee"></input>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="control-group span18">
				<label class="control-label">备注：</label>
				<div class="controls">
					<textarea rows="10" cols="10" id="notes"></textarea>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="offset4" style="padding:50px;" id="add">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="saveBtn">确定</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-no'" id="resetBtn">取消</a>
			</div>
		</div>
	</form>
</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_json.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.validatebox.extends.js"></script>
<script type="text/javascript" src="${ctx}/js/module/archive/node/item.js"></script>
<script>
	BUI.use('module/archive/agentFund/agentFundManagerIn',function(AgentFundManagerIn){
		new AgentFundManagerIn();
	});
</script>
</html>