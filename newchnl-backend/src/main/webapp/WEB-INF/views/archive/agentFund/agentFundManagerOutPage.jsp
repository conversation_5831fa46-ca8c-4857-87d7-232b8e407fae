
<%--保证金支出Jsp页面
 * 
 * $Id: agentFundManagerOutPage.jsp,v 1.5 2015/04/28 05:42:07 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
	<title>保证金支出</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
	<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
	<style type="text/css">
		.form-horizontal .controls {
			height: 30px;
		}
		.form-horizontal .control-label {
			height: 30px;
			width: 90px;
			line-height: 20px;
		}

		input[type="text"], input[type="password"], input[type="email"]{
			width:100px;
		}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
<div data-options="region:'west',split:true"  border="false" title="组织树" style="width:200px; padding: 10px;" id="actionTree"></div>
<!-- 提取 -->
<div region="center" title="" border="false" style="padding: 10px;">
	<form class="form-horizontal" id="ff" method="post">
		<input type="hidden" id="regionid" />
		<input id="agentFundOutChannelEntityStatus" type="hidden" name="channelEntityStatus"/>
		<input id="totalFeeOwner" name="totalFee" type="hidden"/>
		保证金提取
		<hr />
		<input name="agentId" id="agentId" type="hidden">

		<div class="row">
			<div class="control-group span6">
				<label class="control-label">帐号<span style="color:red;">*</span>：</label>
				<div class="controls">
					<input type="text" class="easyui-validatebox" data-options="required:true" id="accId" ></input>
				</div>
			</div>
			<div class="control-group span6">
				<label class="control-label">选择科目<span style="color:red;">*</span>：</label>
				<div class="controls">
					<input type="text" class="easyui-combobox"  id="accName"
						   data-options="panelHeight:'200',
									required:true,
										editable:false,
										valueField:'id',
										textField:'text'">
					</input>
				</div>
			</div>
			<%--我修改的代码结束--%>
			<div class="control-group span6" id="business" style="display: none">
				<div class="controls">
					<label>
						<span id="titleName">选择泛渠道网点</span>
						<span style="color:red">*</span>：
					</label>
				</div>
				<div class="controls">
					<input type="text" class="easyui-combobox"  id="nodeId"
						   data-options="panelHeight:'auto',
										required:true,
										editable:false,
										width:100,
										valueField:'id',
										textField:'text'"/>
				</div>
			</div>
			<div id="sp">
				<div style="margin: 2px;">
					</span> <span> <a id="QueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">请选择</a></span>
				</div>
			</div>
			<%--我修改的代码结束--%>
		</div>

		<div class="row">
			<div class="control-group span6">
				<label class="control-label">可支出金额(元)<span style="color:red;">*</span>：</label>
				<div class="controls">
					<input type="text" class="easyui-numberbox" precision="2"  data-options="required:true" id="leftFee" disabled="disabled" formatter="function(value,row,index){return BUI.columnFormatterMoneny(value);}"></input>
				</div>
			</div>
			<div class="control-group span6">
				<label class="control-label">取出金额(元)<span style="color:red;">*</span>：</label>
				<div class="controls">
					<input type="text" class="easyui-numberbox" precision="2"  data-options="required:true" id="totalFee"></input>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="control-group span20" style="margin:10px;">
				<label class="control-label">备注：</label>
				<div class="controls">
					<textarea rows="20" cols="10" id="notes"></textarea>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="offset4" style="padding:50px;margin-left: 60px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="query">查询</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="saveBtn">确定</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-undo'" id="resetBtn">重置</a>
			</div>
		</div>
	</form>
</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_json.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.validatebox.extends.js"></script>
<script type="text/javascript" src="${ctx}/js/module/archive/node/item.js"></script>
<script>
BUI.use('module/archive/agentFund/agentFundManagerOutPage',function(agentFundManagerOutPage){
	new agentFundManagerOutPage();
});
</script>
</html>