<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/header.jsp"%>
<html>
<head>
    <title>网点CRM工号与虚拟工号对应关系管理</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
</head>
<body>
<div class=span24 style="text-align: center;">
</div>

<div class=span24>
    <div class=span13>
        <div class=span4>
            <label >归属组织：</label>
            <input type="text" class="easyui-combobox" id="districtId" name="districtId" style="width:80px"
                   data-options="panelHeight:'auto',
								required:false,
								editable:false,
								valueField:'id',
								textField:'text'"/>
        </div>
        <div class=span4>
            <label >实体名称：</label>
            <input type="text" class="easyui-validatebox"
                   data-options="required:false" id="channelEntityName" name="channelEntityName" style="width:80px"/>
        </div>
        <div class=span2>
            <a id="entityBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">查询</a>
        </div>
        <div class=span3>
            <label>渠道实体列表</label>
        </div>
    </div>

    <div class=span11>
        <div class=span4>
            <label>CRM工号列表</label>
        </div>
    </div>
</div>


<div class=span24>
    <div class="control-group span12"><!-- 实体表 -->
        <table id="entityTable" style="height:300px" data-options="rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">
            <thead>
            <tr>
                <th data-options="checkbox:true"></th>
                <th data-options="field:'channelEntityId',align:'center',width:100,hidden:true">渠道实体ID</th>
                <th data-options="field:'channelEntityName',align:'center',exportable:false,width:230,hidden:false">渠道实体名称</th>
                <th data-options="field:'districtId' ,align:'center',width:140,formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">归属地</th>
            </tr>
            </thead>
        </table>
    </div>
    <div class="control-group span12"><!-- RBOSS组织表 -->
        <table class="easyui-datagrid" id="orgTable" style="height:300px" data-options="rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">
            <thead>
            <tr>
                <th data-options="checkbox:true"></th>
                <th data-options="field:'agentId',align:'center',width:100,hidden:true" id="agentId">网点编号</th>
                <th data-options="field:'crmStaffId',align:'center',width:100,hidden:false" id="crmStaffId">CRM工号</th>
                <th data-options="field:'orgName',align:'center',exportable:false,width:260,hidden:false">组织名称</th>
            </tr>
            </thead>
        </table>
    </div>
</div>





<div class=span24>
    <div class=span24>
        <label>网点CRM工号与虚拟工号对应关系列表</label>
    </div>
    <div class=span24>
        <div class="row">
            <div class="offset8">
                <a id="addBtn" href="javascript:void(0)" class="easyui-linkbutton">虚拟工号新增</a>
                <a id="queryBtn2" href="javascript:void(0)" class="easyui-linkbutton">查询</a>
                <a id="uploadExlBtn" href="javascript:void(0)" class="easyui-linkbutton">导出</a>
                <a id="modifyBtn" href="javascript:void(0)" class="easyui-linkbutton">修改</a>
                <a id="deleteBtn" href="javascript:void(0)" class="easyui-linkbutton">删除配置</a>
            </div>
            <div class="offset6"></div>
        </div>
    </div>
    <div class="row">
        <div class="control-group span24"><!-- 实体组织关系表 -->
            <table id="entityOrgTable" style="height:200px" data-options="rownumbers:true,singleSelect:false,fitColumns:false,striped:true,loadMsg:'加载中...'">
                <thead>
                <tr>
                    <th data-options="checkbox:true"></th>
                    <th data-options="field:'agentId',hidden:true,exportable:false">ID</th>
                    <th data-options="field:'channelEntityName',hidden:true">网点名称</th>
                    <th data-options="field:'orgName',hidden:true">CRM组织名称</th>
                    <th data-options="field:'virtualStaffId',align:'center',width:200,hidden:false">虚拟工号</th>
                    <th data-options="field:'crmStaffId',align:'center',width:200,hidden:false">CRM工号</th>
                    <th data-options="field:'opOperId',align:'center',width:200">录入工号</th>
                    <th data-options="field:'doneDate',align:'center',width:130">录入时间</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script>
    BUI.use('module/archive/virtualStaffIdMatchCrmId',function(VirtualStaffIdMatchCrmId){
        new VirtualStaffIdMatchCrmId();
    });
</script>
</html>