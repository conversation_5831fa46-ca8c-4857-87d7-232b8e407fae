<%@include file="/common/header.jsp"%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<html>
<head>
<title>商圈信息管理</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<div class="container span24">
		<div class="row" style="padding:8px; height:auto">
			<form class="form-horizontal">
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">商圈名称：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" id="circleName"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">商圈级别：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="
									panelHeight:'auto',
									editable:false,
									valueField:'id',
									textField:'text'"
								id="circleLevel">
								
							</select>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">行政区：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="
									panelHeight:'200',
									editable:false,
									valueField:'id',
									textField:'text'"
								id="regionId">
							</select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">归属组织：</label>
						<div class="controls">
							<select class="easyui-combobox"
								data-options="
									panelHeight:'200',
									editable:false,
									valueField:'id',
									textField:'text'"
								id="districtId">
							</select>
						</div>
					</div>
					<div class="span8 offset11">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">搜索</a>
					</div>
				</div>
			</form>
		</div>

		<div id="tb" style="padding:8px; height:auto">
			<div style="margin-bottom:8px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="addBtn">添加</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true" id="editBtn">修改</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="deleteBtn">删除</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true" id="exportBtn">导出</a>
			</div>
		</div>
		<div class="row">
			<table id="commerceCircleTable" title="商圈信息" class="span24" style="height:400px"
				data-options="rownumbers:true,singleSelect:true,loadMsg:'加载中...'">
			    <thead>
			        <tr>
			        	<th data-options="field:'ck',checkbox:true,hidden:'true'"></th>
		    			<th data-options="field:'circleId',exportable:false" hidden="true">商圈ID</th>
		    			<th data-options="field:'circleLevel',width:'100',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50002',value);}" >商圈级别</th>
		    			<th data-options="field:'circleName',width:'300',align:'center'">商圈名称</th>
		    			<th data-options="field:'regionId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10023',value);}">行政区</th>
		    			<th data-options="field:'districtId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">归属组织</th>
			        </tr>
			    </thead>
			</table>
		</div>
	</div>
	
	
	<div id="commerceCircleWindow" class="easyui-window" title="商圈信息编辑" style="height:200px;width:600px;" 
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div  data-options="region:'center',border:false" style="padding: 10px;" >
			<div class="row" style="padding:8px; height:auto">
				<form class="form-horizontal">
					<input type="hidden" id="circle_id"></input>
					<div class="row">
						<div class="control-group span6">
							<label class="control-label">商圈名称：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="circle_name"></input>
							</div>
						</div>
						<div class="control-group span6">
							<label class="control-label">商圈级别：</label>
							<div class="controls">
								<select class="easyui-combobox"
									data-options="
										panelHeight:'auto',
										editable:false,
										required:true,
										valueField:'id',
										textField:'text'"
									id="circle_level">
								</select>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span6">
							<label class="control-label">行政区：</label>
							<div class="controls">
								<select class="easyui-combobox"
									data-options="
										panelHeight:'200',
										editable:false,
										required:true,
										valueField:'id',
										textField:'text'"
									id="region_id">
								</select>
							</div>
						</div>
						<div class="control-group span6">
							<label class="control-label">归属组织：</label>
							<div class="controls">
								<select class="easyui-combobox"
									data-options="
										panelHeight:'200',
										editable:false,
										required:true,
										valueField:'id',
										textField:'text'"
									id="district_id">
								</select>
							</div>
						</div>
					</div>
				
					<div class="row">
						<div class="span6 offset6">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="saveBtn">保存</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</body>

<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/commerceCircle',function(CommerceCircle){
		new CommerceCircle();
	});
</script>
</html>