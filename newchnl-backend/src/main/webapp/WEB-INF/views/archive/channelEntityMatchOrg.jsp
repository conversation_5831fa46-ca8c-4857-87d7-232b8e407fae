<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/header.jsp"%>
<html>
<head>
<title>实体对应关系管理</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
<div class=span24 style="text-align: center;">
	<label>对应关系类型：</label> 
	<select class="easyui-combobox" 
		data-options="
				panelHeight:'auto',
				editable:false,
				valueField:'id',
				textField:'text'"
				id="configType" style="width:300px">
		<%--前边的是channel_node 中的node_kind--下面[]中的内容在ChannelConstants.java中渠道性质可以找到
		 	后边的
		 --%>
				<option value="1-1">渠道自营厅【直营营业厅】--------RBOSS自营厅</option>
				<option value="2-2">渠道合作厅【加盟营业厅】--------RBOSS合作厅</option>                   
				<option value="10-5">渠道代理店【其他网点】--------RBOSS代理店</option>            
	 			<option value="2-4">渠道外包厅【加盟营业厅】--------RBOSS外包厅</option>
			    <option value="9-8">渠道家庭业务代理点【家庭业务代理店】--------RBOSS家庭业务代理点</option>
			    <option value="4-6">渠道星级网点【手机专卖店】--------RBOSS星级网点</option>            
			    <option value="5-6">渠道星级网点【授权代理店】--------RBOSS星级网点</option>            
			    <option value="6-9">渠道连锁网点【手机卖场】--------RBOSS连锁网点</option>            
			    <option value="3-10">渠道购机中心【合作店】--------RBOSS合作店</option>
				<option value="11-5">渠道电子渠道【电子渠道】——RBOSS代理店</option>
			    <option value="12-5">渠道泛渠道【市级泛渠道】——RBOSS代理店</option>
			    <option value="13-5">渠道泛渠道【区级泛渠道】——RBOSS代理店</option>
				<option value="14-5">渠道直销渠道【直销渠道】——RBOSS代理店</option>
				<option value="15-5">渠道直销渠道【随销渠道】——RBOSS代理店</option>
				</select>
		<a id="queryBtn" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'">确定</a>
</div>

<div class=span24>
	<div class=span13>
		<div class=span4>
			<label >归属组织：</label>
				<input type="text" class="easyui-combobox" id="districtId" name="districtId" style="width:80px"
						   data-options="panelHeight:'auto',
								required:false,
								editable:false,
								valueField:'id',
								textField:'text'"/>
		</div>
		<div class=span4>
			<label >实体名称：</label>
			<input type="text" class="easyui-validatebox"
					data-options="required:false" id="channelEntityName" name="channelEntityName" style="width:80px"/>
		</div>
		<div class=span2>
			<a id="entityBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">查询</a>
		</div>
		<div class=span3>
			<label>渠道实体列表</label>
		</div>
	</div>
	
	<div class=span11>	
		<div class="span4">
			<label>组织名称：</label>
			<input type="text" class="easyui-validatebox"
					data-options="required:true" id="orgName" name="orgName" style="width:80px"/>
		</div>
		<div class=span2>
			<a id="orgBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">查询</a>
		</div>
		<div class=span4>
			<label>R-BOSS组织列表</label>
		</div>
	</div>	
</div>
		
			
<div class=span24>
	<div class="control-group span12"><!-- 实体表 -->
		<table id="entityTable" style="height:200px" data-options="rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">
		<thead>
			<tr>
				<th data-options="checkbox:true"></th>
				<th data-options="field:'channelEntityId',align:'center',width:100,hidden:true">渠道实体ID</th>
				<th data-options="field:'channelEntityName',align:'center',exportable:false,width:230,hidden:false">渠道实体名称</th>
				<th data-options="field:'districtId' ,align:'center',width:140,formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">归属地</th>
			</tr>
		</thead>
		</table>		
	</div>
	<div class="control-group span12"><!-- RBOSS组织表 -->
		<table class="easyui-datagrid" id="orgTable" style="height:200px" data-options="rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">
		<thead>
			<tr>
				<th data-options="checkbox:true"></th>
				<th data-options="field:'org_id',align:'center',width:100,hidden:true">组织ID</th>
				<th data-options="field:'org_type',align:'center',width:100,hidden:true">组织类型</th>
				<th data-options="field:'org_name',align:'center',exportable:false,width:260,hidden:false">组织名称</th>
			</tr>
		</thead>
		</table>
	</div>	
</div>
		
	
	


<div class=span24>
	<div class=span24>
		<label>渠道实体与组织对应关系列表</label>
	</div>
	<div class=span24>
		<div class="row">
			<div class="offset8">
				<a id="addBtn" href="javascript:void(0)" class="easyui-linkbutton">增加配置</a>
				<a id="queryBtn2" href="javascript:void(0)" class="easyui-linkbutton">查询</a>
				<a id="uploadExlBtn" href="javascript:void(0)" class="easyui-linkbutton">导出</a>
				<a id="deleteBtn" href="javascript:void(0)" class="easyui-linkbutton">删除配置</a>
			</div>
			<div class="offset6"></div>
	 	</div>
	</div>
	<div class="row">
		<div class="control-group span24"><!-- 实体组织关系表 -->
			<table id="entityOrgTable" style="height:200px" data-options="rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">   
				<thead>   
					<tr>
						<th data-options="checkbox:true"></th>
						<th data-options="field:'channelEntityId',hidden:true,exportable:false">ID</th>
						<th data-options="field:'orgId',hidden:true,exportable:false">OrgId</th>   
						<th data-options="field:'channelEntityName',align:'center',width:200,hidden:false">渠道实体名称</th>   
						<th data-options="field:'districtId' ,align:'center',width:120,formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">归属地</th>   
						<th data-options="field:'orgName',align:'center',width:200">组织名称</th>   
						<th data-options="field:'doneDate',align:'center',width:130">操作时间</th> 
					</tr>   
				</thead>   
			</table>  
		</div>
	</div>
</div>

</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
 	BUI.use('module/archive/channelEntityMatchOrg',function(ChannelEntityMatchOrg){
 		new ChannelEntityMatchOrg();
 	});
</script>
</html>