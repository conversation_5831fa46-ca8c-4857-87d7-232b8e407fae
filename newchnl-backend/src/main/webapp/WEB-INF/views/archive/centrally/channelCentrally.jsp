<%--
 * 
 * $Id: channelCentrally.jsp,v 1.13 2014/11/12 06:06:50 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>集中地信息管理</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
  
</head>
<body>
	<div class="container">
	  	 <div class="row">
			<form class="form-horizontal">
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">归属组织：</label>
						  <div class="controls">
							<input type="text" class="easyui-combobox"  id="districtId" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input> 
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">行政区：</label>
						  <div class="controls">
							<input type="text" class="easyui-combobox"  id="regionId" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
						</div>
					</div>
				<div class="control-group span8">
						<label class="control-label">集中地名称：</label>
						  <div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" id="centralName"></input>
						</div>
					</div>
					
					<div class="span20 offset18">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">搜索</a>
					</div>
				</div>
			</form>
		</div>
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="addBtn">添加</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true" id="editBtn">修改</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="deleteBtn">删除</a>
			</div>
		</div>
		<div class="row span24">
			<table id="channelCentrallyTable" title="集中地信息"
				data-options="pageSize:10,height:435,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
			    <thead>
			        <tr>
			        	<th data-options="field:'ck',checkbox:true"></th>
		    			<th data-options="field:'centralId'" hidden="true">集中地ID</th>
		    			<th data-options="field:'centralName',width:'200',align:'center'">集中地名称</th>
		    			<th data-options="field:'centralPosition',width:'300',align:'center'">地理描述</th>
		    			<th data-options="field:'regionId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10023',value);}">行政区</th>
		    			<th data-options="field:'districtId',width:'100',align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">归属组织</th>
			        </tr>
			    </thead>
			</table>
		</div>
	</div>
	<!-- 弹出层，添加 -->
 <div id="channelCentrallyWindow" class="easyui-window" title="集中地信息编辑"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div  data-options="region:'center',border:false" style="padding: 10px;" >
			<div class="row" style="padding:8px; height:auto">
				<form class="form-horizontal">
					<input type="hidden" id="central_id"></input>
					<div class="row">
						<div class="control-group span6">
							<label class="control-label">集中地名称<s>*</s>：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="central_name"></input>
							</div>
						</div>
					</div>
					<div class="row">
					    <div class="control-group span6">
							<label class="control-label">行政区<s>*</s>：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="region_id" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
							</div>
						 </div>
					<div class="control-group span6">
							<label class="control-label">归属组织<s>*</s>：</label>
							 <div class="controls">
								<input type="text" class="easyui-combobox" id="district_id" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
							</div>
						</div>
					</div>
						<div class="row">
						  <div class="control-group span10">
							<label class="control-label">地理描述<s>*</s>：</label>
							 <div class="controls">
								<textarea rows="4" cols="5" id="central_position" data-options="iconCls:'icon-reload',closable:true"></textarea>
							</div>
						</div>
					<div style="padding:30px;padding-top:80px;text-align: center;">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="saveBtn">保存</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/centrally/channelCentrally',function(ChannelCentrally){
		new ChannelCentrally();
	});
</script>
</html>