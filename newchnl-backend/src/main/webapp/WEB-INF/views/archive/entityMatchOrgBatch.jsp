<%--
 * 
 * $Id: entityMatchOrgBatch.jsp,v 1.6 2014/09/24 02:01:34 linfeng Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@include file="/common/header.jsp" %>
<html>
<head>
    <title>网点实体批量导入</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet"
          type="text/css"/>
    <link rel="stylesheet" type="text/css"
          href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css"
          href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
<!-- 容器开始 -->

<div class="container span24">
    <div class="row">
        <form class="form-horizontal">
            <fieldset class="span24">
                <legend>查询/导出可被绑定的网点列表</legend>
                <div class="row">
                    <div class="span6 control-group">
                        <label class="control-label">归属组织：</label>
                        <div class="controls">
                            <select class="easyui-combobox"
                                    data-options="panelHeight:'300',editable:false,valueField:'id',textField:'text'"
                                    id="district_id">
                            </select>
                        </div>
                    </div>
                    <div class="span6 control-group">
                        <label class="control-label">门店类别：</label>
                        <div class="controls">
                            <select class="easyui-combobox"
                                    data-options="panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
                                    id="node_kind"></select>
                        </div>
                    </div>
                    <div class="span6 control-group">
                        <label class="control-label">网点名称：</label>
                        <div class="controls">
                            <input type="text" class="easyui-validatebox"
                                   data-options="required:false" id="node_name"/>
                        </div>
                    </div>
                    <div class="row  offset8">
                        <a href="javascript:void(0)" class="easyui-linkbutton"
                           data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
                    </div>
                </div>
            </fieldset>
        </form>

    </div>

    <!-- operate 开始 -->
    <div id="tb">
        <div>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true"
               id="export">导出</a>
        </div>
    </div>
    <!-- operate 结束 -->

    <!-- 数据显示开始 -->
    <div class="control-group span20"><!-- 实体表 -->
        <table id="entityTable" style="height:200px"
               data-options="rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">
            <thead>
            <tr>
                <th data-options="checkbox:true"></th>
                <th data-options="field:'channelEntityName',align:'center',width:230,hidden:false">渠道实体名称</th>
                <th data-options="field:'channelEntityId',align:'center',width:120">门店编号</th>
                <th data-options="field:'districtId' ,align:'center',width:180,formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">
                    归属组织
                </th>
                <th data-options="field:'confiType' ,align:'center',width:180,formatter: function(value,row,index){return BUI.columnFormatter('10033',value);}">
                    门店类别
                </th>
            </tr>
            </thead>
        </table>
    </div>
</div>
<!-- 容器结束 -->
<div class="container span24">
    <div class=span24 style="text-align: center;">
        <fieldset class="span18">
            <legend>批量实体文件导入</legend>
            <div class="row">
                <div class="control-group span24">
                    <label>对应关系类型：</label>
                    <select class="easyui-combobox"
                            data-options="
							panelHeight:'auto',
							editable:false,
							valueField:'id',
							textField:'text'"
                            id="configType" style="width:300px">
                        <!-- 把外包厅字段设置为11,和实体对应菜单不一样,避免渠道和CRM网点类型的一对多的关系
                             value=11与value=12之所以与channelEntityMmatchOrg.jsp中的node_kind不一样，是为了避免
                             value值出现两个相同的值，这里在后台进行转换com.ailk.newchnl.service.EntityImportTaskServiceImpl.executeNodeMatch
                         -->
                        <option value="1">渠道自营厅【直营营业厅】--------RBOSS自营厅</option>
                        <option value="2">渠道合作厅【加盟营业厅】--------RBOSS合作厅</option>
                        <option value="10">渠道代理店【其他网点】--------RBOSS代理店</option>
                        <option value="11">渠道外包厅【加盟营业厅】--------RBOSS外包厅</option>
                        <option value="9">渠道家庭业务代理点【家庭业务代理店】--------RBOSS家庭业务代理点</option>
                        <option value="4">渠道星级网点【手机专卖店】--------RBOSS星级网点</option>
                        <option value="5">渠道星级网点【授权代理店】--------RBOSS星级网点</option>
                        <option value="6">渠道连锁网点【手机卖场】--------RBOSS连锁网点</option>
                        <option value="3">渠道购机中心【合作店】--------RBOSS合作店</option>
                        <option value="12">渠道电子渠道【电子渠道】——RBOSS代理店</option>
                        <option value="13">渠道泛渠道【市级泛渠道】——RBOSS代理店</option>
                        <option value="14">渠道泛渠道【区级泛渠道】——RBOSS代理店</option>
                        <option value="15">渠道直销渠道【直销渠道】——RBOSS代理店</option>
                        <option value="16">渠道直销渠道【随销渠道】——RBOSS代理店</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="control-group span24">
                    <div>
                        <label>实体对应关系</label>
                        <label>模板下载：
                            <a href="#" id="chnlNodeOrgRelation">渠道网点与R-BOSS组织对应关系模板.xls</a>
                        </label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="control-group span24">
                    <label class="control-label">导入文件：</label>
                    <input type="file" data-options="required:true" style="width: 340px" id="fileinput" name="file"/>
                    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel'"
                       id="import">导入</a>
                </div>
            </div>
        </fieldset>
    </div>
</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript"
        src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js"
        data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
    BUI.use('module/archive/entityMatchOrgBatch', function (EntityMatchOrgBatch) {
        new EntityMatchOrgBatch();
    });
</script>
</html>