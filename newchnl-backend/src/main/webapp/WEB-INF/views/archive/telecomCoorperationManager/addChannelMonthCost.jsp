
<%--合作厅和外包厅可变成本录入

 * 
 * $Id: addChannelMonthCost.jsp,v 1.2 2015/02/18 18:18:06 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>合作厅和外包厅可变成本录入</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet"
	type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet"
	type="text/css" />
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css"
	href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
<style type="text/css">
.form-horizontal .controls {
	height: 30px;
}

.form-horizontal .control-label {
	height: 30px;
	width: 100px;
	line-height: 20px;
}

input[type="text"],input[type="password"],input[type="email"] {
	width: 100px;
}
</style>
</head>
<body class="easyui-layout" style="padding: 10px;">

	<!-- 负责人录入 -->
	<div region="center" title="" border="false" style="padding: 10px;">
		<div class="row">
			<form class="form-horizontal" id="ff">
				成本信息
				<hr />
				<div class="row">
					<div class="control-group span10">
						<label class="control-label">加盟厅：<s>*</s></label>
						<div class="controls">
							<!-- <input type="text" class="easyui-validatebox" data-options="required:false" name="channelEntityName" id="channelEntityName" readonly="readonly"></input> -->
							<input name="channelEntityName" id="channelEntityName" style="width: 150px;" class="easyui-combobox"></input>
						</div>
						<div id="sp">
								<div style="margin: 2px;">
									<span> <input id="key" class="easyui-validatebox" style="width: 90px;" />
									</span>
									 <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								<div style="margin: 2px;">
									<ul id="tt"></ul>
								</div>
							</div>
					</div>
					<div class="control-group span10">
						<label class="control-label">月份：</label>
						<div class="controls">
							<select class="easyui-combobox"data-options="panelHeight:'auto',required:false,editable:false,valueField:'id',textField:'text'"name="fullyear" id="fullyear"></select>年 
							<select class="easyui-combobox" id="fullmonth" data-options="panelHeight:'auto', required:false,editable:false,valueField:'id',textField:'text'"></select>月
						</div>
					</div>

				</div>
				<div class="row">
					<div class="control-group span10">
						<label class="control-label">水费：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox"  precision="2" id="waterFee" date-options="required:true" />
						</div>
					</div>
					<div class="control-group span10">
						<label class="control-label">电费：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" precision="2"  id="powerFee"  date-options="required:true" />
						</div>
					</div>
					<div class="control-group span10">
						<label class="control-label">取暖费：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" precision="2"  id="heatFee" date-options="required:true"  />
						</div>
					</div>
				</div>

				<div class="row">
					<div class="control-group span10">
						<label class="control-label">办公用品及耗材：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" precision="2" id="suppyFee" date-options="required:true" />
						</div>
					</div>
					<div class="control-group span10">
						<label class="control-label">人工成本及劳务费总额：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox" precision="2" id="lableCost" date-options="required:true"  />
						</div>
					</div>
					<div class="control-group span10">
						<label class="control-label">其他日常费用：<s>*</s></label>
						<div class="controls">
							<input type="text" class="easyui-numberbox"precision="2"  id="otherFee" date-options="required:true"  />
						</div>
					</div>
				</div>
				<div class="row">
					<div class="span8 offset13">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="saveBtn">保存</a>

					</div>
				</div>
			</form>
		</div>

		<!-- 数据显示开始 -->
		<div class="row">
			<table id="monthCostTable" class="easyui-datagrid span24"
				style="width: 1500px;"
				data-options="pageSize:10,height:380,pagination:true,striped:true,loadMsg:'加载中...'">
				<thead>
					<tr>
						<th data-options="field:'nodeId',align:'center',width:'100'">渠道编号</th>
						<th data-options="field:'monthStr',align:'center',width:'100'">成本月份</th>
						<th data-options="field:'doneDate',width:'100',align:'center'">操作时间</th>
						<th data-options="field:'waterFee',width:'100',align:'center'">水费</th>
						<th data-options="field:'powerFee',width:'100',align:'center'">电费</th>
						<th data-options="field:'heatFee',width:'100',align:'center'">取暖费</th>
						<th data-options="field:'supplyFee',width:'100',align:'center'">办公用品及耗材</th>
						<th data-options="field:'laborCost',width:'100',align:'center'">人工成本及劳务费总额</th>
						<th data-options="field:'otherFee',width:'100',align:'center'">其他日常费用</th>
						<th data-options="field:'opId',width:'100',align:'center'">操作员</th>
						<th data-options="field:'orgId',width:'100',align:'center'">操作员组织</th>
					<!-- 	<th data-options="field:'recId',width:'100',align:'center'">recId</th>
						<th data-options="field:'recStatus',width:'100',align:'center'">recStatus</th>
						<th data-options="field:'ext1',width:'100',align:'center'">ext1</th>
						<th data-options="field:'ext2',width:'100',align:'center'">ext2</th> -->
					
						
						
					</tr>
				</thead>
			</table>
		</div>
	</div>

</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript"src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript"src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<!-- 年月日期控件 -->
<script type="text/javascript"src="${ctx}/js/module/archive/channelSupportOffce/yearMonthTime.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js"data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/telecomCoorperationManager/addChannelMonthCost',
			function(AddChannelMonthCost) {
				new AddChannelMonthCost();
			});
</script>
</html>