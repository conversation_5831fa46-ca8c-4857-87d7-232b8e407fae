<%--
 * 
 * $Id: realName.jsp,v 1.11 2015/03/25 11:45:37 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>实名制信息管理</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	
</head>
<body >
	<div class="container">
		<div class="row"">
			<form class="form-horizontal" id="ff">
			基本信息<hr/>
			<div class="row">
	        	<div class="control-group span8">
				<label class="control-label">门店名称：</label>
					<div class="controls">
					   	<input type="text" class="easyui-validatebox" style="width:150px;" id="channelEntityName"/> 
					</div>
				</div>
				<div class="control-group span8">
					<label class="control-label">归属组织：</label>
					<div class="controls">
					   	<input type="text" class="easyui-combobox" style="width:150px;" id="districtId" 
						   data-options="panelHeight:'200',
								editable:false,
								valueField:'id',
								textField:'text'"/>
					</div>
				</div>
				<div class="control-group span8">
					<label class="control-label">行政区：</label>
					<div class="controls">
						<input type="text" class="easyui-combobox" style="width:150px;" id="ext2" 
							data-options="panelHeight:'200',
									editable:false,
									valueField:'id',
									textField:'text'"/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="control-group span8">
					<label class="control-label">门店类别：</label>
					<div class="controls">
						<input type="text" class="easyui-combobox" style="width:150px;" id="nodeKind"  
							data-options="panelHeight:'200',
									editable:false,
									valueField:'id',
									textField:'text'"/>
					</div>
				</div>
				<div class="control-group span8">
					<label class="control-label">网点编号：</label>
					<div class="controls">
						<input type="text" class="easyui-validatebox" style="width:150px;"
								id="channelEntitySerial" ></input>
					</div>
				</div>
				<div class="control-group span8">
						<label class="control-label">合作方：</label>
						<div class="controls">
							<input id="cc" style="width:150px;" />
						</div>
						<div id="sp">
							<div style="margin: 2px;">
								<span>
									<input id="key" class="easyui-validatebox" style="width:90px;" /> 
								</span>
								<span>
									<a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
								</span>
							</div>
							<div style="margin: 2px;">
								<ul id="tt"></ul> 
							</div>
						</div>
					 </div>
					 <div class="offset20 control-group">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">查询</a>
					    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-reload" id="resetBtn">清空</a>
					  </div>
					</div>
			</form>
		</div>
			<div class="row span24">
				<table id="realnameQueryTable" title="实名制信息" 
				        data-options="pageSize:10,height:435,rownumbers:true,singleSelect:true,fitColumns:true,pagination:true,striped:true,loadMsg:'加载中...'">   
				    <thead>   
				        <tr> 
			    			<th data-options="field:'nodeId',exportable:false" hidden="false">网点ID</th>
			    			<th data-options="field:'districtId',width:'80',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}" >属地</th>
			    			<th data-options="field:'nodeKind',width:'80',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('10033',value);}">网点类型</th>
			    			<th data-options="field:'channelEntityName',width:'80',align:'center'">网点名称</th>
			    			<th data-options="field:'nodeAddr',width:'140',align:'center'">网点地址</th>
			    			<th data-options="field:'realNameSysFlag',width:'100',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50032',value);}">实名制授权</th>
			    			<th data-options="field:'realNameSys',width:'120',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50031',value);}">具备实名制功能</th>
			    			<th data-options="field:'nodeYearCheckId',width:'100',align:'center'">年检编号</th>
			    			<th data-options="field:'chckStatusName',width:'120',align:'center'">年检是否合格</th>
			    			<th data-options="field:'yearCheckRealNameStatus',width:'140',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50031',value);}">年检实名制授权</th>
			    			<th data-options="field:'checkDate',width:'160',align:'center'">年检录入时间</th>
			    			<th data-options="field:'nodeLevel',width:'80',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('10008',value);}">网点星级</th>
			    			<th data-options="field:'channelEntityStatus',width:'80',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('10003',value);}">网点状态</th>
						</tr>    
				    </thead>   
				</table>  
			</div>
		<form class="form-horizontal" id="saveForm" method="post">
		<div class="row span24">
 			 网点实名制修改<hr/>
 			<div class="row">
				<div class="control-group span8">
				    <input id="node_id" type="hidden"/>
					<label class="control-label">网点名称：</label>
					<div class="controls">
						<input type="text" class="easyui-textbox"id="channel_entity_name" style="width:150px;"
								name="channelEntityName" disabled="disabled"></input>
					</div>
				</div>
				<div class="control-group span8">
					<label class="control-label">具备实名制功能：</label>
					<div class="controls">
					   	<input type="text" class="easyui-combobox" id="realName_sys" style="width:150px;" name="realNameSysFlag"
						   data-options="panelHeight:'auto',
								editable:false,
								valueField:'id',
								textField:'text'"/>
					</div>
				</div>
				<div class="control-group span8">
					<label class="control-label">实名制授权：</label>
					<div class="controls">
					   	<input type="text" class="easyui-combobox" id="realName_sys_flag" style="width:150px;" name="realNameSys"
						   data-options="panelHeight:'auto',
								editable:false,
								valueField:'id',
								textField:'text'"/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="offset20 control-group">
					<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="saveBtn">提交</a>
					<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel'" id="exportBtn">导出</a>
				</div>
			</div>
		</div>	
		</form>	
		<!-- 操作结束 -->
    </div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use(['module/archive/nodeRealName/realName'],function(RealName){
	new RealName();
});
</script>
</html>