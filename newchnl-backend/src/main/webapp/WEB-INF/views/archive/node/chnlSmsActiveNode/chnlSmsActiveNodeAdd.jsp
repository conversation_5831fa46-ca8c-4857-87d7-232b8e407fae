<%--
 * 
 * $Id: chnlSmsActiveNodeAdd.jsp,v 1.9 2014/11/14 16:58:06 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>卡号分离短信激活门店信息录入</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
  
</head>
<body>
	<div class="container">
		<div class="row"">
			<form class="form-horizontal" id="ff">
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">门店名称：</label>
						  <div class="controls">
						 	<input type="text" class="easyui-validatebox" data-options="required:false" id="smsNodeName"></input>
						 </div>
					</div>
					    <div class="control-group span8">
						    <label class="control-label">归属组织：</label>
						<div class="controls">
							<input type="text" class="easyui-combobox"  id="districtId" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
						</div>
					</div>
				  <div class="control-group span8">
						<label class="control-label">行政区：</label>
						  <div class="controls">
							<input type="text" class="easyui-combobox"  id="regionId" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
						</div>
					   </div>
					  </div>
					   <div class="row">
					     <div class="control-group span8">
						   <label class="control-label">门店类型：</label>
						     <div class="controls">
							   <input type="text" class="easyui-combobox"  id="smsNodeKind" 
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input> 
						</div>
					 </div>
					<div class="control-group span8">
						   <label class="control-label">网点编号：</label>
						     <div class="controls">
							  <input type="text" class="easyui-validatebox" data-options="required:false" id="channelEntitySerial"></input>
						 </div>
					</div>
				<div class="control-group span8">
						<label class="control-label">合作方：</label>
						  <div class="controls">
							<input  id="channelEntityId" style="width: 120px;" />
						</div>
						  <div id="sp">
								<div style="margin: 2px;">
									<span> <input id="key" class="easyui-validatebox" style="width: 90px;" />
									</span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								  <div style="margin: 2px;">
									<ul id="tt"></ul>
								</div>
						</div>
					</div>
			 </div>
			 <div class="row">
				<div class="offset20 control-group">
					<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">查询</a>
					<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-reload" id="resetBtn">重置</a>
				 </div>
				</div>
			  </form>
		</div>
		<div id="tb"">
			<div style="margin-bottom:8px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="addBtn">录入</a>
			</div>
		</div>
		<div class="row">
			<table id="chnlSmsActiveNodeTable" class="easyui-datagrid span24" title="查询结果"
      		data-options="pageSize:10,height:400,pagination:true,rownumbers:true,singleSelect:true,fitColumns:true,striped:true,loadMsg:'加载中...'">
		      <thead>   
		          <tr>  
		            <th data-options="field:'ck',checkbox:true"></th> 
		            <th data-options="field:'districtId', formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}"  hidden="true">归属区域</th>
		            <th data-options="field:'regionId',formatter: function(value,row,index){return BUI.columnFormatter('10023',value);}" hidden="true">归属组织</th>
		            <th data-options="field:'unifyCode'" hidden="true">门店编码</th>
		            <th data-options="field:'nodeAddr'" hidden="true">门店地址</th>
		            <th data-options="field:'channelEntityName',width:130,align:'center'">网点名称</th>   
		            <th data-options="field:'frontUseArea',width:120,align:'center'">通讯类营业面积</th>   
		            <th data-options="field:'yhcardLimit',width:130,align:'center'">月有号卡销量</th>   
		            <th data-options="field:'cellSaleMonthly',width:100,align:'center'">月手机销量</th>   
		            <th data-options="field:'czcardLimit',width:130,align:'center'">月充值卡销量</th>   
		         </tr>   
		    </thead>   
		</table> 
		</div>
	</div>
	
	<!-- 弹出层，网点信息录入 -->
	<div id="chnlSmsActiveNodeWindow" class="easyui-window" title="卡号分离门店信息录入"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div class="container" >
			<div class="row" style="padding:8px; height:auto">
				<form class="form-horizontal">
					<input type="hidden" id="central_id"></input>
					基本信息1
					<hr />
					  <div class="row">
						<div class="control-group span8">
							<label class="control-label">门店名称<s>*</s>：</label>
							  <div class="controls">
								 <input type="text" class="easyui-validatebox" data-options="required:true" id="channel_entityName" disabled="disabled"></input>
							  </div>
						</div>
						  <div class="control-group span8">
							<label class="control-label">归属组织<s>*</s>：</label>
							  <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="district_id" disabled="disabled"></input>
							 </div>
						</div>
					</div>
					 <div class="row">
					   <div class="control-group span8">
							<label class="control-label">行政区<s>*</s>：</label>
							  <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="region_id" disabled="disabled"></input>
							  </div>
						</div>
						<div class="control-group span8">
							<label class="control-label">门店编码<s>*</s>：</label>
							  <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="unify_code" disabled="disabled"></input>
							  </div>
						</div>
						  
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">门店地址<s>*</s>：</label>
								<div class="controls">
								   <input type="text" class="easyui-validatebox" data-options="required:true" id="node_addr" disabled="disabled"></input>
								</div>
						</div>
					</div>
					基本信息2
					<hr />
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">店主姓名<s>*</s>：</label>
							   <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:false" id="owner_name"></input>
							  </div>
						</div>
						   <div class="control-group span8">
							<label class="control-label">系统接入方式<s>*</s>：</label>
								<div class="controls">
								   <input type="text" class="easyui-combobox" id="join_type" 
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
								</div>
						</div>
						 
					</div>
					 <div class="row">
					 	<div class="control-group span13">
							<label class="control-label">店员积分卡手机号码<s>*</s>：</label>
							  <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:false" maxlength="11" id="dyjf_bill_id"></input>
							  </div>
						</div>
					 </div>
					<div class="row">
						<div class="offset10">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="saveBtn">保存</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/node/chnlSmsActiveNode/chnlSmsActiveNodeAdd',function(ChnlSmsActiveNodeAdd){
		new ChnlSmsActiveNodeAdd();
	});
</script>
</html>