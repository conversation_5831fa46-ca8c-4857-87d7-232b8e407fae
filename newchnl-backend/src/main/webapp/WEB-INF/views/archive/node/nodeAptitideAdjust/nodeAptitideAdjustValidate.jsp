<!--
 * 
 * $Id: nodeAptitideAdjustValidate.jsp,v 1.1 2014/11/26 03:30:45 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>网点资质调整审核</title>
<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_json.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript">
var _width = $(document).width();
var _height = $(document).height();
$("#layoutObj").css("height",  _height-30).css("width",_width-30);
BUI.use('module/archive/node/nodeAptitideAdjust/NodeAptitideAdjustValidate',
function(NodeAptitideAdjustValidate) {
	new NodeAptitideAdjustValidate();
});
</script>

</html>