<%--
 * 
 * $Id: batchImportPage.jsp,v 1.5 2014/10/27 12:07:43 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<div class="container">
		<!-- 客户经理批量导入开始 -->
		<div class="row">
			<fieldset class="span24">
				<legend>客户经理批量导入</legend>
				<form class="form-horizontal" enctype="multipart/form-data" id="ff" method="post">
					<div class="row">
						<div class="span6 text-right">
							客户经理文件
						</div>
						<div class="control-group offset8">
							<label class="control-label">模板下载：</label>
							<div class="controls">
								<a href="#" id="customer">客户经理信息模板</a>
							</div>
						</div>
					</div>
					
					<div class="row">
						<div class="span6 text-right">
							导入文件
						</div>
						<div class="control-group offset8">
							<div class="controls">
								<input type="file" id="batchManager" name="batchManager" />
							</div>
						</div>
					</div>
					<div class="row">
						<div class="offset10">
							<a id="importBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-ok'">批处理</a>
							<input type="reset" value="重置">
						</div>
					</div>
				</form>
			</fieldset>
		</div>
		<!-- 客户经理批量导入结束 -->
		
		<!-- 导入结果 -->
		<div class="row">
			<fieldset class="span24">
				<legend>信息</legend>
				<!-- 正确信息 -->
				<div class="span11" style="margin-right: 20px;">
					<table id="successGrid" class="easyui-datagrid" style="height: 240px;"
					        data-options="fitColumns:true,singleSelect:true">   
					    <thead>   
					        <tr>   
					            <th data-options="field:'managerName',width:10,align:'center'">姓名</th>   
					            <th data-options="field:'branchId',width:10,align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50445',value);}">分公司</th>   
					            <th data-options="field:'billId',width:10,align:'center'">客户经理号码</th> 
					        </tr>   
					    </thead>   
					</table>
				</div>
				<!-- 错误信息 -->
				<div>
					<table id="errorGrid" class="easyui-datagrid" style="height: 240px;"
					        data-options="fitColumns:true,singleSelect:true">   
					    <thead>   
					        <tr>   
					            <th data-options="field:'managerName',width:10,align:'center'">姓名</th>   
					            <th data-options="field:'branchId',width:10,align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50445',value);}">分公司</th>   
					            <th data-options="field:'billId',width:10,align:'center'">客户经理号码</th> 
					            <th data-options="field:'ext5',width:20,align:'center'">错误信息</th>   
					        </tr>   
					    </thead>   
					</table>
				</div>
			</fieldset>
		</div>
		<!-- 导入结果-->
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<script>
	BUI.use('module/archive/node/accountManager/batchImportPage',function(BatchImportPage){
		new BatchImportPage();
	});
</script>
</html>