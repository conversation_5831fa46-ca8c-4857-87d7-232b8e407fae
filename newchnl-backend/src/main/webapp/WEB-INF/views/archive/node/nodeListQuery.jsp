<%--
 * 
 * $Id: nodeListQuery.jsp,v 1.27 2015/08/10 07:20:43 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@include file="/common/header.jsp" %>
<html>
<head>
    <title>title</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
</head>
<body>
<!-- 容器开始 -->
<div class="container">
    <div class="row">
        <div class="row">
            <form class="form-horizontal">
                <div class="row">
                    <div class="span7 control-group">
                        <label class="control-label">网点名称：</label>
                        <div class="controls">
                            <input type="text" class="easyui-validatebox" data-options="required:false" id="node_name"/>
                        </div>
                    </div>
                    <div class="span7 control-group">
                        <label class="control-label">网点编号：</label>
                        <div class="controls">
                            <input type="text" class="easyui-validatebox" id="channel_entity_serial"
                                   data-options="required:false"/>
                        </div>
                    </div>
                    <div class="span7 control-group">
                        <label class="control-label">归属区域：</label>
                        <div class="controls">
                            <input type="text" class="easyui-combobox"
                                   data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
                                   id="district_id"/>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="span7 control-group">
                        <label class="control-label">行政区：</label>
                        <div class="controls">
                            <input type="text" class="easyui-combobox"
                                   data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
                                   id="region_id"/>
                        </div>
                    </div>
                    <div class="span7 control-group">
                        <label class="control-label">四级渠道分类：</label>
                        <div class="controls">
                            <input type="text" class="easyui-combobox"
                                   data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
                                   id="node_kind"/>
                        </div>
                    </div>
                    <div class="span7 control-group">
                        <label class="control-label">五级渠道分类：</label>
                        <div class="controls">
                            <input type="text" class="easyui-combobox"
                                   data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
                                   id="node_type"/>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="control-group span16">
                        <label class="control-label">合作方：</label>
                        <div class="controls">
                            <input id="cc" style="width: 150px;"/>
                        </div>
                        <div id="sp">
                            <div style="margin: 2px;">
									<span> <input id="key" class="easyui-validatebox" style="width: 90px;"/>
									</span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton"
                                                      data-options="iconCls:'icon-search'"></a>
									</span>
                            </div>
                            <div style="margin: 2px;">
                                <ul id="tt"></ul>
                            </div>
                        </div>
                    </div>
                    <div class="span7 control-group">
                        <div class="controls">
                            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'"
                               id="queryBtn">搜索</a>
                        </div>
                        <div class="controls">
                            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-reload'"
                               id="resetBtn">重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- operate 开始 -->
    <div id="tb">
        <div>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-ok',plain:true"
               id="viewBtn">查看</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true"
               id="editBtn">修改</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true"
               id="exitBtn">退出</a>
            <a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true"
               id="exportBtn">基础信息导出</a>
            <a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true"
               id="allExportBtn">全量导出</a>
        </div>
    </div>
    <!-- operate 结束 -->

    <!-- 数据显示开始 -->
    <div class="row span24">
        <table id="detaNodeList" title="查询结果"
               data-options="pageSize:10,height:350,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
            <thead>
            <tr>
                <th data-options="field:'ck',checkbox:true"></th>
                <th data-options="field:'channelEntityId',hidden:true">网点编号</th>
                <th data-options="field:'channelClassFirst',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('10056',value);}}">
                    一级渠道分类
                </th>
                <th data-options="field:'channelClassSecond',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('10057',value);}}">
                    二级渠道分类
                </th>
                <th data-options="field:'channelClassThird',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('10058',value);}}">
                    三级渠道分类
                </th>
                <th data-options="field:'nodeKind',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('10033',value);}}">
                    四级渠道分类
                </th>
                <th data-options="field:'nodeType',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50007',value);}}">
                    五级渠道分类
                </th>
                <th data-options="field:'districtId',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('10002',value);}}">
                    归属组织
                </th>
                <th data-options="field:'channelEntityName',width:120">网点全名</th>
                <th data-options="field:'parentEntityName',width:100">分公司/代理商</th>
                <th data-options="field:'nodeAddr',width:250">门店地址</th>
                <th data-options="field:'nodeLevel',align:'center',width:90,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('10008',value);}}">
                    新网点星级
                </th>
                <th data-options="field:'orgName',width:150">实体对应关系</th>
                <th data-options="field:'useArea',align:'center',width:90">通信类营业面积</th>
                <th data-options="field:'simSaleMonthly',align:'center',width:90,formatter: function(value,row,index){
							if(value==null || value==''){
								return '';
							}else{
								return BUI.columnFormatter('50014',value);
							}
						}">月号码销量
                </th>
                <th data-options="field:'cellSaleMonthly',align:'center',width:90,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50015',value);}}">
                    月手机销量
                </th>

                <!-- <th data-options="field:'systemConnection',align:'center',width:90,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50011',value);}}">系统接入</th>
                <th data-options="field:'systemConnectionTypeA',align:'center',width:100">终端销售</th>
                <th data-options="field:'systemConnectionTypeB',align:'center',width:100">PC写卡</th>
                <th data-options="field:'systemConnectionTypeC',align:'center',width:100">空选机写卡</th>
                <th data-options="field:'systemConnectionTypeD',align:'center',width:100">宽带销售</th> -->

                <th data-options="field:'systemExt0',align:'center',width:90,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50011',value);}}">
                    系统接入方式
                </th>
                <th data-options="field:'systemExt1TypePC',align:'center',width:100">PC接入</th>
                <th data-options="field:'systemExt1TypeKXJ',align:'center',width:100">空选机接入</th>
                <th data-options="field:'systemExt1TypeXYWD',align:'center',width:100">小移微店</th>

                <th style="display: block"
                    data-options="field:'ext2',align:'center',width:90,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50011',value);}}">
                    业务承载类型
                </th>
                <th data-options="field:'businessExt3TypeBKXK',align:'center',width:100">白卡写卡</th>
                <th data-options="field:'businessExt3TypeSMDJ',align:'center',width:100">实名登记</th>
                <th data-options="field:'businessExt3TypeZDXS',align:'center',width:100">终端销售</th>
                <th data-options="field:'businessExt3TypeKDYW',align:'center',width:100">宽带业务</th>

                <th data-options="field:'beneficialScan',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50012',value);}}">
                    鉴别仪扫描
                </th>
                <th data-options="field:'gztCheck',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50013',value);}}">
                    国政通效验
                </th>
                <th data-options="field:'isCentral',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50018',value);}}">
                    是否在集中地
                </th>
                <th data-options="field:'centralName',width:100">集中地名称</th>
                <th data-options="field:'centralPosition',width:120">集中地位置</th>
                <th data-options="field:'ext4',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50004',value);}}">
                    是否授权网点
                </th>
                <th data-options="field:'isAuthorized',align:'center',width:100" >是否异业授权</th>
                <th data-options="field:'ext0',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50004',value);}}">
                    合同是否上传
                </th>
                <th data-options="field:'rel_relationName_1',align:'center',width:100">联系人</th>
                <th data-options="field:'rel_relationMobile_1',align:'center',width:100">联系人电话</th>
                <th data-options="field:'ext5',align:'center',width:100">采购订单号</th>
                <th data-options="field:'ext6',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('82832',value);}}">
                    是否源于变更营业厅
                </th>
                <th data-options="field:'ext7',align:'center',width:100">原门店名称</th>
                <th data-options="field:'contractNumber1',align:'center',width:100">合同编号</th>
                <th data-options="field:'contractMarkTarget',align:'center',width:100">合同名称</th>
                <th data-options="field:'contractBeginDate',align:'center',width:100">合同开始时间</th>
                <th data-options="field:'contractEndDate',align:'center',width:100">合同结束时间</th>
                <th data-options="field:'passOldMonth',align:'center',width:100">合同递延月份</th>
                <th data-options="field:'amountIncludingTax',width:'100',align:'center'">合同含税总额</th>
                <th data-options="field:'amountExcludingTax',width:'100',align:'center'">合同不含税总额</th>
                <th data-options="field:'vendorCode',width:'100',align:'center'">相对方编码</th>
                <th data-options="field:'vendorNameC',width:'100',align:'center'">相对方名称</th>
                <th data-options="field:'is5G',width:'100',align:'center',formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50004',value);}}">是否5G厅</th>
                <th data-options="field:'isRLXS',width:'100',align:'center',formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50004',value);}}">是否顺差让利销售厅店</th>
                <th data-options="field:'trustees',width:'100',align:'center',formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('60001',value);}}">直供代理方</th>
                <th data-options="field:'isStrategicCooperation',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('50004',value);}}">
                    是否战略合作
                </th>
                <th data-options="field:'strategicCooperationType',align:'center',width:100,formatter: function(value,row,index){if(value==null || value==''){return '';}else{return BUI.columnFormatter('10073',value);}}">
                    战略合作类型
                </th>
            </tr>
            </thead>
        </table>
    </div>
    <!-- 数据显示结束 -->
</div>
<!-- 容器结束 -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" defer="defer">
    window.onload=function(){
    BUI.use('module/archive/node/nodeListQuery', function (NodeListQuery) {
        new NodeListQuery();
    });
    }
</script>
</html>