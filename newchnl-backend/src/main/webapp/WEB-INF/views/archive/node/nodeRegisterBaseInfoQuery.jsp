<%--
 * 
 * $Id: nodeRegisterBaseInfoQuery.jsp,v 1.2 2014/08/28 09:36:14 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>门店基站注册信息查询</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
  
</head>
<body>
	<div class="container easyui-layout" style="margin: 10px 0px">
		<fieldset class="span24">
			<legend>查询条件</legend>
			<form class="form-horizontal" id="ff" method="post">
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">门店名称：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" id="entityName" />
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">所属组织：</label>
						<div class="controls">
							<input id="cc" style="width:150px;" />
						</div>
						<div id="sp">
							<div style="margin: 2px;">
								<span>
									<input id="key" class="easyui-validatebox" style="width:90px;" /> 
								</span>
								<span>
									<a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
								</span>
							</div>
							<div style="margin: 2px;">
								<ul id="tt"></ul> 
							</div>
						</div>
						
					</div>
					<div class="control-group span8">
						<label class="control-label">是否注册：</label>
						<div class="controls">
							<input type="text" class="easyui-combobox" id="regFlag"
								data-options="panelHeight:'auto',
										editable:false,
										valueField:'id',
										textField:'text'" />
						</div>
					</div>
				</div>
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">网点性质：</label>
						<div class="controls">
							<input type="text" class="easyui-validatebox" id="nodeKind"
								data-options="panelHeight:'auto',
										editable:false,
										valueField:'id',
										textField:'text'" />
						</div>
					</div>
				</div>
			</form>
			
			<div class="row">
				<div class="control-group offset17">
					<a id="queryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">查询</a>
					<a id="clearBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-undo'">清空</a>
					<a id="exportBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-excel'">导出</a>
				</div>
			</div>
		</fieldset>
		
		<fieldset class="span24">
			<legend>查询结果</legend>
			
			<div class="row">
				<table id="nodeGrid" class="easyui-datagrid" style="height: 300px;"
						data-options="fitColumns:true,singleSelect:true,pagination:true,rownumbers:true,striped:true">   
					<thead>   
						<tr>
					    	<th data-options="checkbox:true"></th>
					        <th data-options="field:'channelEntityId',align:'center',width:10">门店ID</th>    
					        <th data-options="field:'regFlag',align:'center',width:10,formatter: function(value,row,index){return BUI.columnFormatter('50446',value);}">注册状态</th>   
					        <th data-options="field:'nodeKind',align:'center',width:10,formatter: function(value,row,index){return BUI.columnFormatter('10033',value);}">网点性质</th>   
					        <th data-options="field:'parentName',align:'center',width:10">归属组织</th> 
					        <th data-options="field:'channelEntityName',align:'center',width:10">门店名称</th>   
					        <th data-options="field:'nodeAddr',align:'center',width:10">门店地址</th>   
					        <th data-options="field:'relationName',align:'center',width:10">联系人</th> 
					        <th data-options="field:'relationMobile',align:'center',width:10">联系电话</th>  
						</tr>   
					</thead>   
				</table>
			</div>
			
			<div class="row" style="margin-top: 15px;">
				<form class="form-horizontal" id="qf" method="post">
					<div class="control-group span8">
						<label class="control-label">起始时间：</label>
						<div class="controls">
							<input type="text" id="startTime" class="easyui-datebox" data-options="editable:false" />
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">结束时间：</label>
						<div class="controls">
							<input type="text" id="endTime" class="easyui-datebox" data-options="editable:false" />
						</div>
					</div>
				</form>
				<div class="control-group span8">
					<div class="control-group">
						<a id="zcBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">查注册信息</a>
						<a id="yhBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">查优惠平台</a>
					</div>
				</div>
			</div>
			
			<div class="row">
				<div style="color:red;">查注册信息:查询渠道本地记录的门店基站注册信息  查优惠平台:通过调用华为MML接口查询优惠小区平台记录的门店基站注册信息</div>
				<table id="resultGrid" class="easyui-datagrid" style="height: 300px;"
						data-options="fitColumns:true,singleSelect:true,pagination:true,rownumbers:true,striped:true">   
					<thead>   
						<tr>
					    	<th data-options="checkbox:true"></th>
					        <th data-options="field:'nodeName',align:'center',width:10">门店名称</th>    
					        <th data-options="field:'longitude',align:'center',width:10">经度</th>   
					        <th data-options="field:'latitude',align:'center',width:10">纬度</th>   
					        <th data-options="field:'billId',align:'center',width:10">客服经理号码</th> 
					        <th data-options="field:'perferareaId',align:'center',width:10">优惠区编号</th>   
					        <th data-options="field:'lacId',align:'center',width:10">LAC编号</th>   
					        <th data-options="field:'cellId',align:'center',width:10">CELL编号</th> 
					        <th data-options="field:'sDoneDate',align:'center',width:10">创建时间</th>  
						</tr>   
					</thead>   
				</table>
			</div>
		</fieldset>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script>
  BUI.use('module/archive/node/nodeRegisterBaseInfoQuery',function(NodeRegisterBaseInfoQuery){
    new NodeRegisterBaseInfoQuery();
  });
</script>
</html>