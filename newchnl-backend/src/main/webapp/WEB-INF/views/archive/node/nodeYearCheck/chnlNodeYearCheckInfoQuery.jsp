<!--
 * 
 * $Id: chnlNodeYearCheckInfoQuery.jsp,v 1.8 2014/11/21 15:00:32 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>网点年检查询</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
<!-- 容器开始 -->
	<div class="container">
		<div class="row" style="padding:8px; height:auto">
			<div class="row">
				<form class="form-horizontal">
					<div class="row">
						<div class="span7 control-group">
							<label class="control-label">门店名称：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
						  			id="channelEntityName"></input>
							</div>	
						</div>
						<div class="span7 control-group">
							<label class="control-label">网点性质：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="nodeKind" name="nodeKind"
									data-options="panelHeight:'200px',
											editable:false,
											valueField:'id',
											textField:'text'"/>
							</div>	
						</div>
						<div class="span7 control-group">
							<label class="control-label">归属组织：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="districtId"
								   data-options="panelHeight:'200px',
										editable:false,
										valueField:'id',
										textField:'text'"/>
							</div>	
						</div>
					</div>
					<div class="row">
						<div class="span7 control-group">
							<label class="control-label">行政区：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="regionId"
									data-options="panelHeight:'200px',
											editable:false,
											valueField:'id',
											textField:'text'"/>
							</div>	
						</div>
						<div class="span7 control-group">
							<label class="control-label">网点编号：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
									data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'" id=channelEntitySerial />
							</div>
						</div>
						<div class="span7 control-group">
							<label class="control-label">合作方：</label>
							<div class="controls">
								<input id="agentId" style="width: 150px;" />
							</div>
							<div id="sp">
								<div style="margin: 2px;">
									<span> <input id="key" class="easyui-validatebox" style="width: 90px;" />
									</span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								<div style="margin: 2px;">
									<ul id="tt"></ul>
								</div>
							</div>
						</div>
						<div class="span5 control-group">
							<div class="controls">
								<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
							</div>
							<div class="controls">
								<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-reload'" id="resetBtn">重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
		
		<!-- 数据显示开始 -->
		<div class="row span24">
			<table id="nodeYearChackList" title="查询结果  （请双击行查看年检详情信息）" data-options="pageSize:10,height:380,pagination:true,rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">
			    <thead>
			        <tr>
			        <th data-options="field:'channelEntityId',hidden:true,align:'center',width:150">网点编号</th>
		    			<th data-options="field:'channelEntityName',hidden:false,align:'center',width:150">网点名称</th>
		    			<th data-options="field:'frontUseArea',hidden:false,align:'center',width:100">营业面积</th>
		    			<th data-options="field:'simSaleMonthly',align:'center',width:100,formatter: function(value,row,index){return BUI.columnFormatter('50014',value);}">月有号卡销量</th>
		    			<th data-options="field:'cellSaleMonthly',align:'center',width:100,formatter: function(value,row,index){return BUI.columnFormatter('50015',value);}" >月手机销量</th>
			        </tr>
			    </thead>
			</table>
		</div>
		<!-- 数据显示结束 -->
	</div>
	<!-- 容器结束 -->
	
	<!-- 年检信息查询开始 -->
	<div id="winYearCheck" class="easyui-window" title="年检信息查询" style="width:640px;height:400px"   
      	data-options="iconCls:'icon-save',modal:true,closed:true">
      	<div class="container span12">
			<div class="row">
				<table id="yearCheckTable" title="年检信息"
					data-options="pageSize:10,height:300,width:590,pagination:true,rownumbers:true,singleSelect:true,fitColumns:false,striped:false,loadMsg:'加载中...'">
				    <thead>
				        <tr>
			    			<th data-options="field:'nodeYearCheckId',width:'80'">年检编号</th>
			    			<th data-options="field:'checkStatus',width:'80',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50033',value);}">是否合格</th>
			    			<th data-options="field:'ext5',width:'100',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50032',value);}">实名制授权</th>
			    			<th data-options="field:'checkDate',width:'100',align:'center'">年检日期</th>
			    			<th data-options="field:'doneDate',width:'100',align:'center'">操作日期</th>
				        </tr>
				    </thead>
				</table>
			</div>
		</div>   
	</div>
	<!-- 年检信息查询结束 -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use(['module/archive/node/nodeYearCheck/chnlNodeYearCheckInfoQuery'],function(ChnlNodeYearCheckInfoQuery){
	new ChnlNodeYearCheckInfoQuery();
});
</script>
</html>