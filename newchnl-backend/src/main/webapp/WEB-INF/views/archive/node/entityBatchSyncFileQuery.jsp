<!--
 * 
 * $Id: entityBatchSyncFileQuery.jsp,v 1.4 2014/09/18 09:15:21 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>实体批量进度查询</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<!-- 容器开始 -->
	<div class="container">
		<div class="row" style="padding:8px; height:auto">
			<div class="row">
				<form class="form-horizontal">
					<div class="row">
						<div class="span8 control-group">
							<label class="control-label">开始时间：</label>
							<div class="controls">
								<input type="text" class="easyui-datebox"
						  			id="begDate"></input>
							</div>	
						</div>
						<div class="span8 control-group">
							<label class="control-label">结束时间：</label>
							<div class="controls">
								<input type="text" class="easyui-datebox"
						  			id="endDate"></input>
							</div>	
						</div>
						<div class="span6 control-group">
							<label class="control-label">工号：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:false" id="userName"/>
							</div>
						</div>
						<div class="span3 control-group">
							<div class="controls">
								<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
		
		<!-- operate 开始 -->
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true" id="exportBtn">导出选中行的错误文件</a>
			</div>
		</div>
		<!-- operate 结束 -->
		
		<!-- 数据显示开始 -->
		<div class="row span24">
			<table id="detaEntityBatchList" title="查询结果" data-options="pageSize:10,height:400,toolbar:'#tb',pagination: true,rownumbers:true,singleSelect:true,fitColumns:true,striped:true,loadMsg:'加载中...'">
			    <thead>
			        <tr>
			        	<th data-options="field:'ck',checkbox:true"></th>
			        	<th data-options="field:'fileSeq',hidden:false,align:'center',width:120">文件编号</th>
		    			<th data-options="field:'fileType',hidden:false,align:'center',width:200,formatter: function(value,row,index){return BUI.columnFormatter('50447',value);}">文件类型</th>
		    			<th data-options="field:'sumrecord',hidden:false,align:'center',width:120">文件总记录</th>
		    			<th data-options="field:'wrongrecord',align:'center',width:100">错误记录</th>
		    			<th data-options="field:'okrecord',align:'center',width:100" >有效记录</th>
		    			<th data-options="field:'dealDt',align:'center',width:240">处理时间</th>
		    			<th data-options="field:'status',align:'center',width:100,formatter: function(value,row,index){return BUI.columnFormatter('50448',value);}">状态</th>
		    			<th data-options="field:'errorfile',align:'center',width:200">错误文件</th>
						<th data-options="field:'userName',align:'center',width:150">工号</th>
		    			<th data-options="field:'notes',align:'center',width:200">备注</th>
			        </tr>
			    </thead>
			</table>
		</div>
		<!-- 数据显示结束 -->
	</div>
	<!-- 容器结束 -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script>
	BUI.use(['module/archive/node/entityBatchSyncFileQuery'],function(EntityBatchSyncFileQuery){
		new EntityBatchSyncFileQuery();
	});
</script>
</html>