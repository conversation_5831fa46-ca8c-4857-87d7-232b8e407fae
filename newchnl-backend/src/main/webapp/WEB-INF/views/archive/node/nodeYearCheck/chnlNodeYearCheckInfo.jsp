<!--
 * 
 * $Id: chnlNodeYearCheckInfo.jsp,v 1.4 2014/11/21 14:59:52 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
<!-- 容器开始 -->
	<div class="container">
		<div class="row" style="padding:8px; height:auto">
			<div class="row">
				<form class="form-horizontal">
					<div class="row">
						<div class="span7 control-group">
							<label class="control-label">门店名称：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
						  			id="channelEntityName"></input>
							</div>	
						</div>
						<div class="span7 control-group">
							<label class="control-label">网点性质：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="nodeKind" name="nodeKind"
									data-options="panelHeight:'200px',
											editable:false,
											valueField:'id',
											textField:'text'"/>
							</div>	
						</div>
						<div class="span7 control-group">
							<label class="control-label">归属组织：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="districtId"
								   data-options="panelHeight:'200px',
										editable:false,
										valueField:'id',
										textField:'text'"/>
							</div>	
						</div>
					</div>
					
					<div class="row">
						<div class="span7 control-group">
							<label class="control-label">行政区：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="regionId"
									data-options="panelHeight:'200px',
											editable:false,
											valueField:'id',
											textField:'text'"/>
							</div>	
						</div>
						<div class="span7 control-group">
							<label class="control-label">网点编号：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox"
									data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'" id=channelEntitySerial />
							</div>
						</div>
						<div class="span7 control-group">
							<label class="control-label">合作方：</label>
							<div class="controls">
								<input id="agentId" style="width: 150px;" />
							</div>
							<div id="sp">
								<div style="margin: 2px;">
									<span> <input id="key" class="easyui-validatebox" style="width: 90px;" />
									</span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								<div style="margin: 2px;">
									<ul id="tt"></ul>
								</div>
							</div>
						</div>
						<div class="span5 control-group">
							<div class="controls">
								<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="queryBtn">搜索</a>
							</div>
							<div class="controls">
								<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-reload'" id="resetBtn1">重置</a>
							</div>
						</div>
					</div>
					
				</form>
			</div>
		</div>
		
		<!-- operate 开始 -->
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true" id="addYearBtn">新增年检信息</a>
			</div>
		</div>
		<!-- operate 结束 -->
		
		<!-- 数据显示开始 -->
		<div class="row span24">
			<table id="nodeYearChackList" title="查询结果" data-options="pageSize:10,height:380,pagination:true,toolbar:'#tb',rownumbers:true,singleSelect:true,fitColumns:false,striped:true,loadMsg:'加载中...'">
			    <thead>
			        <tr>
			        	<th data-options="field:'channelEntityId',hidden:true,align:'center',width:150">网点编号</th>
		    			<th data-options="field:'channelEntityName',hidden:false,align:'center',width:150">网点名称</th>
		    			<th data-options="field:'nodeLevel',align:'center',width:100,formatter: function(value,row,index){return BUI.columnFormatter('10008',value);}"">网点星级</th>
		    			<th data-options="field:'channelEntityStatus',align:'center',width:100,formatter: function(value,row,index){return BUI.columnFormatter('10003',value);}">网点状态</th>
		    			<th data-options="field:'nodeKind',align:'center',width:120,formatter: function(value,row,index){return BUI.columnFormatter('10033',value);}" >网点性质</th>
		    			<th data-options="field:'staffNum',align:'center',width:100" >营业员数量</th>
		    			<th data-options="field:'rel_relationName_3',align:'center',width:100" >申请人</th>
		    			<th data-options="field:'nodeAddr',align:'center',width:200" >网点地址</th>
			        </tr>
			    </thead>
			</table>
		</div>
		<!-- 数据显示结束 -->
	</div>
	<!-- 容器结束 -->
	
	<!-- 年检信息录入开始 -->
	<div id="winYearCheckInfo" class="easyui-window" title="年检信息查询" style="width:640px;height:180px"   
      	data-options="iconCls:'icon-save',modal:true,closed:true">
      	<div class="row" style="padding-top:20px; height:auto">
			<div class="row">
				<form class="form-horizontal" id="f_yearCheckInfo">
					<input type="hidden" id="nodeId" name="nodeId"> 
					<div class="row">
						<div class="span8 control-group">
							<label class="control-label">年检编号：</label>
							<div class="controls">
								<input type="text" class="easyui-numberbox" data-options="required:true"
						  			id="nodeYearCheckId" name="nodeYearCheckId"></input>
							</div>	
						</div>
						<div class="span8 control-group">
							<label class="control-label">是否合格：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="checkStatus" name="checkStatus"
									data-options="panelHeight:'200px',
											required:true,
											editable:false,
											valueField:'id',
											textField:'text'"/>
							</div>	
						</div>
						<div class="span8 control-group">
							<label class="control-label">实名制授权：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="ext5" name="ext5"
								   data-options="panelHeight:'200px',
								   		required:true,
										editable:false,
										valueField:'id',
										textField:'text'"/>
							</div>	
						</div>
					</div>
					<div class="row">
						<div class="offset7">
							<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="saveBtn">保存</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="resetBtn">重置</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
	<!-- 年检信息录入结束 -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
BUI.use(['module/archive/node/nodeYearCheck/chnlNodeYearCheckInfo'],function(ChnlNodeYearCheckInfo){
	new ChnlNodeYearCheckInfo();
});
</script>
</html>