<%--
 * 
 * $Id: chnlSmsActiveNodeModify.jsp,v 1.10 2014/11/21 11:09:17 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>卡号分离短信激活门店信息修改</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
  
</head>
<body>
	<div class="container">
		<div class="row">
			<form class="form-horizontal" id="ff">
				<div class="row">
					<div class="control-group span6">
						<label class="control-label">门店名称：</label>
						  <div class="controls">
						 	<input type="text" class="easyui-validatebox" data-options="required:false" id="smsNodeName"></input>
						 </div>
					</div>
					    <div class="control-group span6">
						    <label class="control-label">归属组织：</label>
						<div class="controls">
							<input type="text" class="easyui-combobox"  id="districtId" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
						</div>
					</div>
				  <div class="control-group span6">
						<label class="control-label">行政区：</label>
						  <div class="controls">
							<input type="text" class="easyui-combobox"  id="regionId" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
						</div>
					   </div>
					  </div>
					  <div class="row">
					     <div class="control-group span6">
						   <label class="control-label">门店类型：</label>
						     <div class="controls">
							   <input type="text" class="easyui-combobox"  id="smsNodeKind" 
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input> 
						</div>
					 </div>
					<div class="control-group span6">
						   <label class="control-label">门店编码：</label>
						     <div class="controls">
							  <input type="text" class="easyui-validatebox" data-options="required:false" id="channelEntitySerial"></input>
						 </div>
					</div>
				<div class="control-group span8">
						<label class="control-label">合作方：</label>
						  <div class="controls">
							<input  id="channelEntityId" style="width: 150px;" />
						</div>
						  <div id="sp">
								<div style="margin: 2px;">
									<span> <input id="key" class="easyui-validatebox" style="width: 90px;" />
									</span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								  <div style="margin: 2px;">
									<ul id="tt"></ul>
								</div>
						</div>
					</div>
			 </div>
			 <div class="row">
				<div class="offset15 control-group">
					<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">查询</a>
					<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-reload" id="resetBtn">重置</a>
				 </div>
				</div>
			  </form>
		</div>
		<div id="tb">
			<div style="margin-bottom:8px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true" id="editBtn">修改</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" id="deleteBtn">删除</a>
			</div>
		</div>
		<div class="row">
			<table id="chnlSmsActiveNodeTable" class="easyui-datagrid span24" title="查询结果"
      		data-options="pageSize:10,height:400,pagination:true,rownumbers:false,singleSelect:true,fitColumns:true,striped:true,loadMsg:'加载中...'">
		      <thead>   
		          <tr>  
		            
		            <th data-options="field:'nodeId',width:100,align:'center'" hidden="true">门店id</th>
		            <th data-options="field:'channelEntityName',width:150,align:'center'">门店名称</th>   
		            <th data-options="field:'districtId',width:120,align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">行政区域</th>
		            <th data-options="field:'regionId',width:120,align:'center',formatter: function(value,row,index){return BUI.columnFormatter('10023',value);}">归属组织</th>
		            <th data-options="field:'joinType',width:120,align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50449',value);}">系统接入方式</th>
		            <th data-options="field:'dyjfBillId',width:120,align:'center'">店员积分卡手机号</th>  
		            <th data-options="field:'channelEntitySerial',width:100,align:'center'" >门店编码</th> 
		            <th data-options="field:'ownerName',width:100,align:'center'" >店主姓名</th> 
		             <th data-options="field:'nodeAddr',width:180,align:'center'">门店地址</th> 
		         </tr>   
		    </thead>   
		</table> 
		</div>
	</div>
	
	<!-- 弹出层 网点信息修改 -->
	<div id="chnlSmsActiveNodeWindow" class="easyui-window" title="卡号分离门店信息修改"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div class="container" >
			<div class="row" style="padding:8px; height:auto">
				<form class="form-horizontal">
					<input type="hidden" id="central_id"></input>
					基本信息1
					<hr />
					  <div class="row">
						<div class="control-group span8">
							<label class="control-label">门店名称：<s>*</s></label>
							  <div class="controls">
								 <input type="text" class="easyui-validatebox" data-options="required:true" id="channel_entityName" disabled="disabled"></input>
							  </div>
						</div>
						  <div class="control-group span8">
							<label class="control-label">归属组织：<s>*</s></label>
							  <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="district_id" disabled="disabled"></input>
							 </div>
						</div>
					</div>
					 <div class="row">
					  <div class="control-group span8">
							<label class="control-label">行政区：<s>*</s></label>
							  <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="region_id" disabled="disabled"></input>
							  </div>
						</div>
						<div class="control-group span8">
							<label class="control-label">门店编码：<s>*</s></label>
							  <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true" id="channel_entity_serial" disabled="disabled"></input>
							  </div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">门店地址：<s>*</s></label>
								<div class="controls">
								   <input type="text" class="easyui-validatebox" data-options="required:true" id="node_addr" disabled="disabled"></input>
								</div>
						</div>
					</div>
					基本信息2
					<hr />
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">店主姓名：<s>*</s></label>
							   <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:false" id="owner_name"></input>
							  </div>
						</div>
						   <div class="control-group span8">
							<label class="control-label">系统接入方式：<s>*</s></label>
								<div class="controls">
								   <input type="text" class="easyui-combobox" id="join_type" 
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
								</div>
						</div>
					</div>
					<div class="row">
						<div class="control-group span9">
							<label class="control-label">店员积分卡手机号码：<s>*</s></label>
							  <div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:false" maxlength="11" id="dyjf_bill_id"></input>
							  </div>
						</div>
					</div>
					
					<div class="row">
						<div class="offset10">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="updateBtn">确定</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleBtn">取消</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/node/chnlSmsActiveNode/chnlSmsActiveNodeModify',function(ChnlSmsActiveNodeModify){
		new ChnlSmsActiveNodeModify();
	});
</script>
</html>