<!--
  
  $Id: nodeInfoBatchImport.jsp,v 1.6 2014/10/22 10:51:11 fuqiang Exp $
  Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>网点批量录入</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<style type="text/css">
	.form-horizontal .controls {
		height: 30px;
	}
	.form-horizontal .control-label {
		height: 30px;
		width: 100px;
		line-height: 20px;
	}
    input[type="text"], input[type="password"], input[type="email"]{
		width: 200px;
	}
	</style>
</head>
<body class="easyui-layout" style="padding: 10px;">
	<div data-options="region:'west',split:true" border="false" title="组织树" style="width: 200px; padding: 10px;" id="actionTree"></div>
	<div region="center" title="" border="false" style="padding: 10px;">
		<form class="form-horizontal" id="ff" method="post">
		    <input 	name="parentEntity"	id="parentEntity" type="hidden"></input>
			<fieldset class="span24">
				<legend>网点批量录入</legend>
				<div class="row">
					<div class="control-group span15">
						<label class="control-label">选择上传模板</label>
						<div class="controls">
                            <span style="color:red;">*</span>
                            <input type="text" class="easyui-combobox" data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'" id="nodeKindType" />
						</div>
					</div>
                </div>
                <div class="row">
                    <div class="control-group span15">
                        <label class="control-label">模板下载：</label>
                        <div class="controls">
                            &nbsp;
                            <a href="#" id="directlyOperationNode" style="display: none;">网点信息_直营营业厅.xls</a>
                            <a href="#" id="leagueOperationNode" style="display: none;">网点信息_加盟营业厅.xls</a>
                            <a href="#" id="leagueSocialStoreNode" style="display: none;">网点信息_合作店.xls</a>
                            <a href="#" id="mobileAndAccreditNode" style="display: none;">网点信息_手机专卖店和授权代理店.xls</a>
                            <a href="#" id="mobileStoresNode" style="display: none;">网点信息_手机卖场.xls</a>
                            <a href="#" id="familyAgentNode" style="display: none;">网点信息_家庭业务代理店.xls</a>
 							<a href="#" id="schoolSalesNode" style="display: none;">网点信息_校园直销队.xls</a>
                            <a href="#" id="selfAssistTerminalsNode" style="display: none;">网点信息_自助终端.xls</a>
                            <a href="#" id="otherNode" style="display: none;">网点信息_其他网点.xls</a>
							<a href="#" id="cityAndDistrictLevelNode" style="display: none;">网点信息_泛渠道网点.xls</a>
							<a href="#" id="directAndAlongSalesNode" style="display: none;">网点信息_直销随销渠道网点.xls</a>
                        </div>
                    </div>
                </div>
                <div class="row">
					<div class="control-group span15">
						<label class="control-label">合作方：</label>
                        <div class="controls">
                            <span style="color:red;">*</span>
							<input type="text" class="easyui-validatebox" disabled="disabled" name="parentEntityName" id="parentEntityName" />
						</div>
					</div>
				</div>
				<div class="row">
                    <div class="control-group span15">
                        <label class="control-label">导入文件：</label>
                        <div class="controls">
                            <span style="color:red;">*</span>
                            <input type="file"  data-options="required:true" style="width: 240px"  id="fileinput" name="file"/>
                        </div>
                    </div>
				</div>
			</fieldset>
		</form>
		<div class="row span24">
			<div align="center">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="import">导入</a>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	var nodeKind = "${nodeKind}";
	BUI.use('module/archive/node/nodeInfoBatchImport',function(NodeInfoBatchImport){
		new NodeInfoBatchImport();
	});
</script>
</html>