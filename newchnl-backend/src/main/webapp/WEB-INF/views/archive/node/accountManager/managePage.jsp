<%--
 * 
 * $Id: managePage.jsp,v 1.2 2015/02/24 17:27:46 wangjin Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	
</head>
<body>
	<div class="container">
		<!-- 查询输入开始 -->
		<div class="row">
			<fieldset class="span24">
				<legend>查询条件</legend>
				<form class="form-horizontal" id="qf" method="post">
					<div class="row">
						<div class="control-group span7">
							<label class="control-label">客户经理姓名：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" name="managerName" id="qManagerName" />
							</div>
						</div>
						<div class="control-group span7">
							<label class="control-label">分公司：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="qBranchId" name="branchId" id="qBranchId"
									data-options="panelHeight:'150',
										editable:false,
										valueField:'id',
										textField:'text'" />
							</div>
						</div>
						<div class="control-group span7">
							<label class="control-label">手机号码：</label>
							<div class="controls">
								<input type="text" class="easyui-numberbox" data-options="validType:'cmPhone'" id="qBillId" name="billId" />
							</div>
						</div>
						<div class="control-group span3">
							<a id="queryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">查询</a> 
						</div>
					</div>
				</form>
			</fieldset>
		</div>
		<!-- 查询输入结束 -->
		
		<!-- 查询结果显示开始 -->
		<div class="row">
			<fieldset class="span24">
				<legend>查询结果</legend>
				<table id="datagrid" class="easyui-datagrid" style="height: 240px;"
				        data-options="fitColumns:true,singleSelect:true,pagination:true,rownumbers:true,striped:true">   
				    <thead>   
				        <tr>
				        	<th data-options="checkbox:true"></th>
				        	<th data-options="field:'managerId',hidden:true">客户经理ID</th>    
				            <th data-options="field:'managerName',width:10,align:'center'">姓名</th>   
				            <th data-options="field:'branchId',width:10,align:'center',formatter: function(value,row,index){if(value)return BUI.columnFormatter('50445',value);}">分公司</th>   
				            <th data-options="field:'billId',width:10,align:'center'">客户经理号码</th>   
				        </tr>   
				    </thead>   
				</table>  
			</fieldset>
		</div>
		<!-- 查询结果显示结束-->
		
		<!-- 详细信息开始 -->
		<div class="row">
			<fieldset class="span24">
				<legend>修改区域</legend>
				<form class="form-horizontal" id="uf" method="post">
					<div class="row">
						<div class="control-group span8" style="display: none;">
							<label class="control-label">客户经理ID：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" name="managerId" id="uManagerId" />
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">客户经理姓名：</label>
							<div class="controls">
								<input type="text" class="easyui-validatebox" data-options="required:true,validType:'length[0,32]'" name="managerName" />
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">分公司：</label>
							<div class="controls">
								<input type="text" class="easyui-combobox" id="uBranchId" name="branchId"
									data-options="panelHeight:'150',
										editable:false,
										valueField:'id',
										textField:'text'" />
							</div>
						</div>
						<div class="control-group span8">
							<label class="control-label">手机号码：</label>
							<div class="controls">
								<input type="text" class="easyui-numberbox" data-options="required:true,validType:'cmPhone'" name="billId" />
							</div>
						</div>
					</div>
				</form>
			</fieldset>
		</div>
		<!-- 详细信息结束 -->
		
		<!-- 操作开始 -->
		<div class="row">
			<fieldset class="span24">
				<legend></legend>
				<div class="offset9">
					<a id="saveBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-save'">新增</a>
					<a id="editBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-edit'">修改</a>
					<a id="removeBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-remove'">删除</a>
					<a id="undoBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-undo'">重置</a>
				</div>
			</fieldset>
		</div>
		<!-- 操作结束 -->
		
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.validatebox.extends.js"></script>
<script>
	BUI.use('module/archive/node/accountManager/managePage',function(ManagePage){
		new ManagePage();
	});
</script>
</html>