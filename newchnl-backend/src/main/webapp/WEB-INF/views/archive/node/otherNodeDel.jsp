<%--
 * 
 * $Id: otherNodeDel.jsp,v 1.12 2014/09/12 05:38:06 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>其它网点查看界面</title>
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<!-- here we will place the layout --->
	<div id="layoutObj"></div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/module/archive/node/item.js"></script>
<script>
	$("#layoutObj").css("height", document.documentElement.clientHeight);
	var codeId = "${codeId}";
	var nodeId = "${channelEntityId }";
	BUI.use(['module/archive/node/otherNodeDel'],function(OtherNodeDel){
		new OtherNodeDel();
	});
</script>
</html>