<%--
 * 
 * $Id: chnlSmsActiveNodeQuery.jsp,v 1.9 2014/11/14 16:56:36 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>卡号分离短信激活门店信息查询</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
  
</head>
<body>
	<div class="container">
		<div class="row">
			<form class="form-horizontal" id="ff">
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">门店名称：</label>
						  <div class="controls">
						 	<input type="text" class="easyui-validatebox" data-options="required:false" id="smsNodeName"></input>
						 </div>
					</div>
					    <div class="control-group span8">
						    <label class="control-label">归属组织：</label>
						<div class="controls">
							<input type="text" class="easyui-combobox"  id="districtId" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
						</div>
					</div>
				  <div class="control-group span8">
						<label class="control-label">行政区：</label>
						  <div class="controls">
							<input type="text" class="easyui-combobox"  id="regionId" 
							        data-options="panelHeight:'200',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input>
						</div>
					   </div>
					  </div>
					  <div class="row">
					<div class="control-group span8">
						<label class="control-label">门店类型：</label>
						  <div class="controls">
							 <input type="text" class="easyui-combobox"  id="smsNodeKind" 
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input> 
						</div>
					 </div>
					<div class="control-group span8">
						    <label class="control-label">门店编码：</label>
						     <div class="controls">
							  <input type="text" class="easyui-validatebox" data-options="required:false" id="channelEntitySerial"></input>
						 </div>
					</div>
				 <div class="control-group span8">
						<label class="control-label">合作方：</label>
						  <div class="controls">
							<input  id="channelEntityId" style="width: 150px;" />
						</div>
						  <div id="sp">
								<div style="margin: 2px;">
									<span> <input id="key" class="easyui-validatebox" style="width: 90px;" />
									</span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								  <div style="margin: 2px;">
									<ul id="tt"></ul>
								</div>
						</div>
					</div>
			 </div>
			 <div class="row">
				<div class="offset18 control-group">
					<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">查询</a>
					<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-reload" id="resetBtn">重置</a>
				 </div>
				</div>
			  </form>
		</div>
		<div id="tb">
			<div>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true" id="excelBtn">导出</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true" id="excelOldBtn">导出修改史</a>
			</div>
		</div>
		<div class="row">
			<table id="chnlSmsActiveNodeTable" class="easyui-datagrid span24" title="查询结果"
      		data-options="pageSize:10,height:400,pagination:true,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
		      <thead>   
		          <tr>  
		            <th data-options="field:'ck',checkbox:true"></th>
		            <th data-options="field:'nodeId',exportable:false,width:100,align:'center'" hidden="true">门店id</th>
		            <th data-options="field:'channelEntityName',width:150,align:'center'">门店名称</th>   
		            <th data-options="field:'districtId',width:120,align:'center', formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">归属组织</th>
		            <th data-options="field:'regionId',width:120,align:'center',formatter: function(value,row,index){return BUI.columnFormatter('10023',value);}">行政区域</th>
		            <th data-options="field:'joinType',width:120,align:'center',formatter: function(value,row,index){return BUI.columnFormatter('50449',value);}">系统接入方式</th>
		            <th data-options="field:'dyjfBillId',width:120,align:'center'">店员积分卡手机号</th>  
		            <th data-options="field:'channelEntitySerial',width:120,align:'center'" >门店编码</th> 
		            <th data-options="field:'ownerName',width:100,align:'center'" >店主姓名</th> 
		             <th data-options="field:'nodeAddr',width:120,align:'center'">门店地址</th> 
		         </tr>   
		    </thead>   
		</table> 
		</div>
	</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
	BUI.use('module/archive/node/chnlSmsActiveNode/chnlSmsActiveNodeQuery',function(ChnlSmsActiveNodeQuery){
		new ChnlSmsActiveNodeQuery();
	});
</script>
</html>