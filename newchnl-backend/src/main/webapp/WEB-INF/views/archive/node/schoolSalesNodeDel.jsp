<%--
 * 
 * $Id: schoolSalesNodeDel.jsp,v 1.9 2014/09/11 07:01:37 lijian Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
	<title>校园直销队查看</title>
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<!-- here we will place the layout --->
	<div id="layoutObj"></div>
	<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
	<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
	<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
	<script type="text/javascript" src="${ctx}/js/module/archive/node/item.js"></script>
<script>
	$("#layoutObj").css("height", document.documentElement.clientHeight);
	var nodeId = "${channelEntityId }";
	var codeId="${codeId}";
	//var codeId = "${requestScope.codeId}";
	//var nodeId = "${param.channelEntityId}";
	BUI.use('module/archive/node/schoolSalesNodeDel',function(SchoolSalesNodeDel){
		new SchoolSalesNodeDel(); 
	});
</script>
</html>