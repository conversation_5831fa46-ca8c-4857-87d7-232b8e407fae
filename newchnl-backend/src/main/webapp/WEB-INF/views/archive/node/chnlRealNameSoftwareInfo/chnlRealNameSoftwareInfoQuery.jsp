<%--
 * 
 * $Id: chnlRealNameSoftwareInfoQuery.jsp,v 1.9 2014/11/21 14:44:16 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>实名制拍照软件信息管理</title>
<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">

</head>
<body>
	<div class="container span24">
		<div class="row" style="padding:8px; height:auto">
			<form class="form-horizontal" id="ff">
				<div class="row">
					<div class="control-group span8">
						<label class="control-label">门店名称：</label>
						  <div class="controls">
						    <input type="text" class="easyui-validatebox" data-options="required:false" id="chnnelEntityName"></input>
						 </div>
					</div>
					  <div class="control-group span8">
						<label class="control-label">登录帐号：</label>
						  <div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" id="logName"></input>
						</div>
					</div>
				 <div class="control-group span12">
						<label class="control-label">是否已录入拍照软件：</label>
						  <div class="controls">
						 	<input type="text" class="easyui-combobox"  id="chnlReaNAMEsoftWareInfolJuede" 
							        data-options="panelHeight:'auto',
									required:false,
									editable:false,
									valueField:'id',
									textField:'text'"></input> 
						</div>
					</div>
				</div>
				 <div class="row">
				   <div class="control-group span8">
						<label class="control-label">合作方归属组织：</label>
						  <div class="controls">
							<input type="text" class="easyui-validatebox" data-options="required:false" id="districtId"></input>
						</div>
					</div>
					<div class="control-group span8">
						<label class="control-label">合作方：</label>
						  <div class="controls">
							<input  id="channelEntityId" style="width: 150px;" />
						</div>
						   <div id="sp">
								<div style="margin: 2px;">
									<span> <input id="key" class="easyui-validatebox" style="width: 90px;" />
									</span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'"></a>
									</span>
								</div>
								  <div style="margin: 2px;">
									<ul id="tt"></ul>
								</div>
						</div>
					</div>
				  </div>
				
				<div class="row">
					<div class="span8 offset20">
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" id="queryBtn">搜索</a>
						<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-reload" id="resetBtn">重置</a>
					</div>
				</div>
			</form>
		</div>

		<div id="tb" style="padding:8px; height:auto">
			<div style="margin-bottom:8px">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="addBtn">信息添加</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true" id="editBtn">信息修改</a>
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-excel',plain:true" id="excelBtn">信息导出</a>
			</div>
		</div>
		<div class="row">
			<table id="ChnlRealNameStwareInfoSTable" title="查询结果" class="span24" style="width:1010px;"
				data-options="pageSize:10,height:435,pagination:true,rownumbers:true,singleSelect:true,fitColumns:true,striped:true,loadMsg:'加载中...'">
			    <thead>
			        <tr>
			        	<th data-options="field:'ck',checkbox:true"></th>
			        	<th data-options="field:'nodeId'" hidden="true">网点ID</th>
		    			<th data-options="field:'districtId',width:'100',align:'center',formatter: function(value,row,index){return BUI.columnFormatter('10002',value);}">代理商归属组织</th>
		    			<th data-options="field:'fullName',width:'200',align:'center'">合作方全称</th>
		    			<th data-options="field:'channelEntityName',width:'120',align:'center'">网点名称</th>
		    			<th data-options="field:'logName',width:'100',align:'center'">登录账号</th>
		    			<th data-options="field:'billId',width:'100',align:'center'">手机号码</th>
		    			<th data-options="field:'imei',width:'130',align:'center'">手机IMEI</th>
		    			<th data-options="field:'doneDate',width:'140',align:'center'">最后操作时间</th>
			        </tr>
			    </thead>
			</table>
		</div>
	</div>
	<!-- 弹出层，添加 -->
	<div id="ChnlRealNameStwareInfoSWindow" class="easyui-window" title="实名制信息录入"
		data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
		<div  data-options="region:'center',border:false" style="padding: 10px;" >
			<div class="row" style="padding:8px; height:auto">
		<form class="form-horizontal" id="ff1">
			<input id="node_id" type="hidden" />
			基本信息
					<hr />
					<div class="row">
						<div class="control-group span8">
							<label class="control-label">合作方归属组织：</label>
							<input type="text" class="easyui-combobox" data-options="required:false" id="district_id" disabled="disabled"></input>
						</div>
					   <div class="control-group span8">
						        <label class="control-label">合作方名称：</label>
						   		<input type="text" class="easyui-validatebox" data-options="required:false" id="full_name" disabled="disabled"></input>
						</div>
					</div>
				<div class="row">
				     <div class="control-group span8">
						  <label class="control-label">网点名称：</label>
						   <input type="text" class="easyui-validatebox" data-options="required:false" id="channel_entityName" disabled="disabled"></input>
						</div>
				    </div>
				     	实名制拍照软件信息录入
					<hr />
					  <div class="row">
						<div class="control-group span8">
							<label class="control-label">登录帐号：</label>
							<input type="text" class="easyui-validatebox" data-options="required:false" id="log_name"></input>
						</div>
					<div class="control-group span8">
							<label class="control-label">手机号码：</label>
							<input type="text" class="easyui-validatebox" data-options="required:false,validType:'phone'" maxlength="11" id="bill_id"></input>
						</div>
					</div>
				<div class="row">
						<div class="control-group span8">
							<label class="control-label">手机IMEI：</label>
							<input type="text" class="easyui-validatebox" data-options="required:false"  maxlength="15" id="ime_i"></input>
						</div>
					</div>
				<div class="row">
						 <div class="offset4">
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="saveBtn">确定</a>
							<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleBtn">取消</a>
						</div>
					</div>
			</form>
		</div>
	</div>
</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.validatebox.extends.js"></script>
<script>
	var operateType = "${operateType}";
	BUI.use('module/archive/node/chnlRealNameSoftwareInfo/chnlRealNameSoftwareInfoQuery',function(ChnlRealNameSoftwareInfoQuery){
		new ChnlRealNameSoftwareInfoQuery();
	});
</script>
</html>