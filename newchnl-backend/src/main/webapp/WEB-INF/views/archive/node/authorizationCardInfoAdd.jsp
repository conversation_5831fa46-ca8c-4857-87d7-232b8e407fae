
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
    <title>地推授权牌录入</title>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">

    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet"
          type="text/css" />
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet"
          type="text/css" />
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css"href="${ctx}/assets/easyui/themes/icon.css">
</head>
<body>
<!-- 容器开始 -->
<!-- here we will place the layout --->
<div id="layoutObj"></div>
<!-- 容器结束 -->
<!-- 定义agentInfoViewWindow窗口 -->
<div id="nodeInfoViewWindow" class="easyui-window" title="信息查询结果"
     style="width:450px;height:460px;padding:10px;" data-options="modal:true,closed:true,iconCls:'icon-edit',
			minimizable:false,maximizable:false,resizable:false">
    <!-- 表格将在这里定义 -->
    <table id="queryMyGrid"
           data-options="pageSize:20,pageList:[20,50,100,300,500],height:340,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
        <thead>
        <tr>
            <th data-options="field:'ck',checkbox:true"></th>
            <th data-options="field:'nodeId',hidden:true,align:'center',width:120">网点编码</th>
            <th data-options="field:'nodeName',align:'center',width:355">网点名称</th>
        </tr>
        </thead>
    </table>
    <div class="row">
        <div class="offset4" style="margin-top: 20px">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" id="savesBtn">确定</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-no" id="cancleBtn">取消</a>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_json.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>


<script type="text/javascript"src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<!-- xls导出引入 -->
<script type="text/javascript"src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript"src="${ctx}/js/common/jquery.validatebox.extends.js"></script>

<!-- xls导出引入 -->
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<!-- xls导入引入 -->
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<!-- 年月日期控件 -->
<script type="text/javascript"src="${ctx}/js/module/archive/channelSupportOffce/yearMonthTime.js"></script>
<script>
    var _width = $(document).width();
    var _height = $(document).height();
    $("#layoutObj").css("height",  _height).css("width",_width);
    BUI.use('module/archive/node/authorizationCardInfoAdd',
        function(AuthorizationCardInfoAdd) {
            new AuthorizationCardInfoAdd();
        });
</script>
</html>