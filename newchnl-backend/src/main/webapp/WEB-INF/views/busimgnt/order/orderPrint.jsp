<%--
 * 
 * $Id: orderPrint.jsp,v 1.2 2015/02/10 14:43:54 fuqiang Exp $$
 * Copyright 2015 Asiainfo Technologies(China),Inc. All rights reserved.
 * zhangrp
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@include file="/common/header.jsp" %>
<html>
<head>
    <title>订单签单明细打印</title>
</head>
<%
    java.util.Calendar cal = new java.util.GregorianCalendar();
    int year = cal.get(java.util.Calendar.YEAR);
    int month = cal.get(java.util.Calendar.MONTH) + 1;
    int day = cal.get(java.util.Calendar.DAY_OF_MONTH);
    String strMonth = "" + month + "";
    String strDay = "" + day + "";
    if (month < 10) strMonth = "0" + String.valueOf(month);
    if (day < 10) strDay = "0" + String.valueOf(day);
    String da = year + "-" + strMonth + "-" + strDay;

%>
<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">
    <div align="center">
        <table border="1" width="74%" id="table2" cellspacing="0" cellpadding="0">
            <tr align=center>
                <td align=center>
                    <table cellpadding="0" cellspacing="0" width="574">
                        <tr>
                            <td width="150"></td>
                            <td valign="top" align="center"><b><u><font size="5">订单明细</font></u></b></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td valign="top" colspan="2" align="left"><span style="font-size: 11pt" id="orderId">&nbsp;&nbsp;订单号：</span></td>
                            <td><span style="font-size: 11pt">&nbsp;&nbsp;日期：<%=da %></span></td>
                        </tr>
                        <tr>
                            <td valign="top" colspan="3">
                                <table border="1" width="574" id="table1" cellspacing="0" bordercolor="#000000">
                                    <tr>
                                        <td width="574" colspan=5 align="left" id="agentFullName">签约单位：</td>
                                    </tr>
                                    <tr id="header">
                                        <td width="196" align="center">卡类型</td>
                                        <td width="109" align="center">卡数量</td>
                                        <td width="114" align="center">单价</td>
                                        <td width="137" align="center">合计</td>
                                    </tr>



                                    <tr>
                                        <td colspan=3 width="427" align="left">付费金额</td>
                                        <td width="137" align="right" id="payFee"></td>
                                    </tr>
                                    <tr>
                                        <td colspan=3 width="427" align="left" id="accCode"></td>
                                        <td width="137" align="right" id="fundFee"></td>
                                    </tr>
                                    <tr>
                                        <td colspan=3 width="427" align="left">罚金</td>
                                        <td width="137" align="right" id="fineFee"></td>
                                    </tr>
                                    <tr>
                                        <td colspan=3 width="427" align="left">总计</td>
                                        <td width="137" align="right" id="totalFee"></td>
                                    </tr>
                                    <tr>
                                        <td width="196" align="left">人民币(大写)</td>
                                        <td colspan=3 width="368" align="left" id="totalFeeStr"></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td align="left">经办：</td>
                            <td></td>
                            <td width="158"></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
    <script>
        var order = <%=request.getAttribute("order")%>;
        var orderDtl = <%=request.getAttribute("orderDtl")%>;
        var totalFeeStr = "<%=request.getAttribute("totalFeeStr")%>";
        var agentFullName = "<%=request.getAttribute("agentFullName")%>";
        $("#payFee").html(order.payFee);
        if(order.accCode == '4100001'){
            order.accCode = "明折明扣";
        }else if(order.accCode == '4100002'){
            order.accCode = "代理费";
        }else{
            order.accCode = "明折明扣/代理费";
        }
        $("#accCode").html(order.accCode);
        $("#fundFee").html(formatMoneyFill(order.fundFee, 2));
        $("#fineFee").html(formatMoneyFill(order.fineFee, 2));
        $("#totalFee").html(formatMoneyFill(order.totalFee, 2));
        $("#totalFeeStr").html(totalFeeStr);
        $("#agentFullName").html("签约单位："+agentFullName);
        $("#orderId").html("&nbsp;&nbsp;订单号："+order.orderId)

        var html = "";
        for(var i=0;i<orderDtl.length; i++){
            html = html + "<tr>"+
                "<td width='196' align='left'>"+orderDtl[i].resName+"</td>"+
                "<td width='109' align='right'>"+orderDtl[i].orderResNum+"</td>"+
                "<td width='114' align='right'>"+formatMoneyFill(orderDtl[i].unitPrice/100,2)+"</td>"+
                "<td width='137' align='right'>"+formatMoneyFill(orderDtl[i].unitPrice*orderDtl[i].orderResNum/100,2)+"</td>"+
            "</tr>";
        }
        $(html).insertAfter("#header");


        function formatMoneyFill(value, suffix){
            if (value == "") {
                return "0.00";
            }
            value += "";
            var pointindex = value.indexOf(".");
            var fill = "";
            var fillLen;
            if (pointindex != -1) {
                var end = value.substr(pointindex + 1, value.length - pointindex);
                if (end.length < suffix)
                    fillLen = suffix - end.length;
                else
                    fillLen = 0;
            } else {
                value += ".";
                fillLen = suffix;
            }
            for (var i = 0; i < fillLen; i++) {
                fill += "0";
            }
            return value + fill;
        }


        window.print();
    </script>
</body>
</html>