<%--
 * 
 * $Id: orderstatusquery.jsp,v 1.1 2015/01/18 16:46:07 linfeng Exp $$
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 * huyp
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@include file="/common/header.jsp" %>
<html>
<head>
    <title>空中充值订单新增</title>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>


</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_json.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_export.js"></script> 
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script>
    BUI.use(['module/busimgnt/order/orderstatusquery'],function(OrderStatusQuery){
        new OrderStatusQuery();
    });
</script>
</html>