<%--
 * 
 * $Id: payPrint.jsp,v 1.3 2015/02/11 16:42:45 gongchao Exp $$
 * Copyright 2015 Asiainfo Technologies(China),Inc. All rights reserved.
 * zhangrp
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ page import="com.ailk.newchnl.util.ChannelUtil" %>
<%@include file="/common/header.jsp" %>
<html>
<head>
    <title></title>
</head>
<%
    java.util.Calendar cal = new java.util.GregorianCalendar();
    int year = cal.get(java.util.Calendar.YEAR);
    int month = cal.get(java.util.Calendar.MONTH) + 1;
    int day = cal.get(java.util.Calendar.DAY_OF_MONTH);
    String strMonth = "" + month + "";
    String strDay = "" + day + "";
    if (month < 10) strMonth = "0" + String.valueOf(month);
    if (day < 10) strDay = "0" + String.valueOf(day);
    String da = year + "-" + strMonth + "-" + strDay;

%>
<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">
    <div align="center">
        <table border="0" width="74%" id="table2" cellspacing="0" cellpadding="0" bordercolor="#FFFFFF" >
            <tr align=center>
                <td align=center>
                    <table cellpadding="0" cellspacing="0" width="510">
                        <tr>
                          	<td colspan="3" style="text-align: center;">
								<b><u><font size="5"> 上海移动通信公司收据</font> </u> </b>
							</td>
                        </tr>
                        <tr>
                        		<td colspan="2" align="left">
									<span style="font-size: 9pt" id="doneCode">收据流水号:&nbsp; </span>
							        <span style="font-size: 9pt" id="orderId">订单编号:&nbsp;</span>
								</td>
								
								<td valign="top">
									<span style="font-size: 9pt">&nbsp;日期:<%=da%> </span>
								</td>
                        </tr>
                        <tr>
                            <td valign="top" colspan="3">
                                <table border="1" width="574" id="table1" cellspacing="0" bordercolor="#000000">
                                    <tr>
                                        <td width="574" colspan=5 align="left" id="agentFullName">签约单位：</td>
                                    </tr>
                                    <tr id="header">
                                        <td width="196" align="center">卡类型</td>
                                        <td width="109" align="center">卡数量</td>
                                        <td width="114" align="center">单价</td>
                                        <td width="137" align="center">合计</td>
                                    </tr>



                                    <tr>
                                        <td colspan=3 width="427" align="left">付费金额</td>
                                        <td width="137" align="right" id="payFee"></td>
                                    </tr>
                                    <tr>
                                        <td colspan=3 width="427" align="left" id="accCode"></td>
                                        <td width="137" align="right" id="fundFee"></td>
                                    </tr>
                                    <tr>
                                        <td colspan=3 width="427" align="left">罚金</td>
                                        <td width="137" align="right" id="fineFee"></td>
                                    </tr>
                                    <tr>
                                        <td colspan=3 width="427" align="left">总计</td>
                                        <td width="137" align="right" id="totalFee"></td>
                                    </tr>
                                    <tr>
                                        <td width="196" align="left">人民币(大写)</td>
                                        <td colspan=3 width="368" align="left" id="totalFeeStr"></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                           	<td width="202">
									收款方式:
									<input id="payType" readonly="readonly" size="10"
										style="border-style: solid;border-width: 0px;border-color:#E7E7E7" />
								</td>
								<td width="280">
									出纳:
								</td>
								<td width="120">
									经办:
								</td>
                        </tr>
                        <tr style="padding-top: 10px;"><td colspan="3" id="assignStr"  style="text-align: left;"></td></tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
    <script>
        var order = <%=request.getAttribute("order")%>;
        var orderDtl = <%=request.getAttribute("orderDtl")%>;
        var totalFeeStr = "<%=request.getAttribute("totalFeeStr")%>";
        var agentFullName = "<%=request.getAttribute("agentFullName")%>";
        var chnlResModelDefinition = <%=ChannelUtil._CHNL_RES_MODEL_DEFINITION%>;

        $("#assignStr").html("<%=request.getAttribute("assignStr")%>");

        $("#payFee").html(formatMoneyFill(order.ext3/100, 1));
        if(order.subject == '4100001'){
            order.subject = "明折明扣";
        }else if(order.subject == '4100002'){
            order.subject = "代理费";
        }else{
            order.subject = "明折明扣/代理费";
        }
        $("#accCode").html(order.subject);
        $("#fundFee").html(formatMoneyFill(order.fund/100, 1));
        $("#fineFee").html(formatMoneyFill((order.ext3+order.fund-order.totalCharge)/100,1));
        $("#totalFee").html(formatMoneyFill(order.totalCharge/100, 1));
        $("#totalFeeStr").html(totalFeeStr);
        $("#agentFullName").html("签约单位："+agentFullName);
        $("#orderId").html("&nbsp;&nbsp;订单编号："+order.channelOrderId);
        $("#doneCode").html("收据流水号："+order.doneCode);
        $("#payType").val("现金");

        var html = "";
        for(var i=0;i<orderDtl.length; i++){
            html = html + "<tr>"+
                "<td width='196' align='left'>"+chnlResModelDefinitionFormatter(orderDtl[i].resCode)+"</td>"+
                "<td width='109' align='right'>"+orderDtl[i].orderResNum+"</td>"+
                "<td width='114' align='right'>"+formatMoneyFill(orderDtl[i].unitPrice/100,1)+"</td>"+
                "<td width='137' align='right'>"+formatMoneyFill(orderDtl[i].unitPrice*orderDtl[i].orderResNum/100,1)+"</td>"+
            "</tr>";
        }
        $(html).insertAfter("#header");

		
        function formatMoneyFill(value, suffix){
            if (value == "") {
            	var strs = "0.";
            	for(var j =0;j<suffix;j++ ){
            		strs += "0";
            	}
                return strs;
            }
            value += "";
            var pointindex = value.indexOf(".");
            var fill = "";
            var fillLen;
            if (pointindex != -1) {
                var end = value.substr(pointindex + 1, value.length - pointindex);
                if (end.length < suffix)
                    fillLen = suffix - end.length;
                else
                    fillLen = 0;
            } else {
                value += ".";
                fillLen = suffix;
            }
            for (var i = 0; i < fillLen; i++) {
                fill += "0";
            }
            return value + fill;
        }
        /**
         * 资源定义枚举
         */
       function chnlResModelDefinitionFormatter (resCode){
            for(var i=0;i<chnlResModelDefinition.length;i++){
                if(resCode){
                    if(chnlResModelDefinition[i].resCode != resCode){
                        continue;
                    }
                }
                return chnlResModelDefinition[i].resName;
            }
            return resCode;
        };
		

        window.print();
    </script>
</body>
</html>