<%--
 * 
 * $Id: whitecardreplacement.jsp,v 1.1 2014/12/18 02:27:55 wangjin Exp $$
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 * yecl
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@include file="/common/header.jsp" %>
<html>
<head>
    <title>补/换白卡</title>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
	<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
	<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
	<script>
	
	    BUI.use(['module/busimgnt/order/whitecardreplacement'],function(Whitecardreplacement){
	        new Whitecardreplacement();
	    });
	</script>
</body>
</html>