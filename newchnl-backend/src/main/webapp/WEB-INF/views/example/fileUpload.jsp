<!--
 * 
 * $Id: fileUpload.jsp,v 1.2 2014/08/07 06:36:24 fuqiang Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
<title>title</title>
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/bootstrap/easyui.css">
</head>
<body>
		<input type="file" id="fileinput" name="files"/>
		<button onclick="ajaxFileUpload();">确定</button>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<script>
	function ajaxFileUpload() {
		var fileName = $("#fileinput").val();
		
		$.ajaxFileUpload({
			url: BUI.ctx+'/exampleController/testUpload?fileName='+fileName, // 上传处理程序地址
			secureuri: false,  //是否启用安全提交，默认为false
			fileElementId: 'fileinput', //需要上传的文件域的ID，即<input type="file">的ID
			dataType: 'JSON', //服务器返回的数据类型。可以为xml,script,json,html。如果不填写，jQuery会自动判断。 注意使用大写
			success: function (data, status) {
				if(data.type == "FINISH"){
					alert("上传成功!");
				}else{
					alert(data.msg);
				}
			},
			error: function (data, status, e) {
				alert(e);
			}
		});
	}
	
</script>
</html>