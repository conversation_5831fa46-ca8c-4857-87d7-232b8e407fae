<%--
 *
 * $Id: leagueSocialNodeExeAssessGrade.jsp,v 1.2 2018/08/21 09:30:28 lixs Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
    <title>合作店考核录入</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
</head>
<body>
<!-- 容器开始 -->
<div class="container">
    <div class="row">
        <div class="row">
            <form class="form-horizontal">
                <div class="row">
                    <div class="span7 control-group">
                        <label class="control-label">归属属地：</label>
                        <div class="controls">
                            <input type="text" class="easyui-combobox"
                                   data-options="panelHeight:'200',editable:false,valueField:'id',textField:'text'"
                                   id="district_id"/>
                        </div>
                    </div>
                    <div class="control-group span16">
                        <label class="control-label">代理商简称：</label>
                        <div class="controls">
                            <input id="cc" style="width: 150px;"/>
                        </div>
                        <div id="sp">
                            <div style="margin: 2px;">
                                        <span> <input id="key" class="easyui-validatebox" style="width: 90px;"/>
                                        </span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton"
                                                          data-options="iconCls:'icon-search'"></a>
                                        </span>
                            </div>
                            <div style="margin: 2px;">
                                <ul id="tt"></ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="span7 control-group">
                        <label class="control-label">网点名称：</label>
                        <div class="controls">
                            <input type="text" class="easyui-validatebox" data-options="required:false" id="node_name"/>
                        </div>
                    </div>
                    <div class="control-group span10">
                        <label class="control-label">月份：</label>
                        <div class="controls">
                            <select class="easyui-combobox"data-options="panelHeight:'auto',required:false,editable:false,valueField:'id',textField:'text',readonly:true"name="fullyear" id="fullyear"></select>年
                            <select class="easyui-combobox" id="fullmonth" data-options="panelHeight:'auto', required:false,editable:false,valueField:'id',textField:'text',readonly:true"></select>月
                        </div>
                    </div>
                    <div class="span7 control-group">
                        <div class="controls">
                            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'"
                               id="queryBtn">搜索</a>
                        </div>
                        <div class="controls">
                            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-reload'"
                               id="resetBtn">重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- operate 开始 -->
    <div id="tb">
        <div>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-ok',plain:true"
               id="nodeScoreBtn">网点评分</a>
            <span style="color: red; margin-left: 10px;">* 请勾选需要评分的网点，点击按钮进行网点评分操作</span>
        </div>
    </div>
    <!-- operate 结束 -->

    <!-- 数据显示开始 -->
    <div class="row span20">
        <table id="detaNodeList" title="查询结果"
               data-options="pageSize:10,height:350,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
            <thead>
            <tr>
                <th data-options="field:'ck',checkbox:true"></th>
                <th data-options="field:'nodeId',align:'center'" hidden="true">网点ID</th>
                <th data-options="field:'agentId',align:'center'" hidden="true">代理商ID</th>
                <th data-options="field:'districtId',align:'center'" hidden="true">属地Id</th>
                <th data-options="field:'districtName',align:'center',width:100">属地</th>
                <th data-options="field:'agentName',align:'center',width:200">代理商</th>
                <th data-options="field:'nodeName',align:'center',width:300">网点名称</th>
                <%--                <th data-options="field:'assessScore',align:'center',width:100">得分</th>--%>
            </tr>
            </thead>
        </table>
    </div>
    <!-- 数据显示结束 -->
    <div region="center" title="" border="false" style="padding: 10px;">
        <form class="form-horizontal" id="ff" method="post">
            <fieldset class="span24" style="margin-top: 100px">
                <legend>合作店考核批量录入</legend>
                <div class="row">
                    <div class="control-group span15">
                        <label class="control-label">模板下载：</label>
                        <div class="controls">
                            &nbsp;
                            <a href="#" id="socialStoreAssessImport">合作店考核批量录入.xls</a>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="control-group span15">
                        <label class="control-label">导入文件：</label>
                        <div class="controls">
                            <span style="color:red;">*</span>
                            <input type="file"  data-options="required:true" style="width: 240px"  id="files" name="files"/>
                        </div>
                    </div>
                </div>
            </fieldset>
        </form>
        <div class="row span20">
            <div align="center">
                <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="import">文件批量导入</a>
                &nbsp; &nbsp; &nbsp;
                <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'" id="export">导入结果下载</a>
            </div>
        </div>
    </div>
</div>

<!-- 容器结束 -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/ajaxfileupload.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script type="text/javascript" defer="defer">
    window.onload=function() {
        BUI.use('module/report/assess/socialStoreAssessAdd', function (socialStoreAssessAdd) {
            new socialStoreAssessAdd();
        });
    }
</script>
</html>