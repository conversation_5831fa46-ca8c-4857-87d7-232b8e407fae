<%--
  Created by IntelliJ IDEA.
  User: asus
  Date: 2022/8/11
  Time: 11:01
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
    <title>社会电渠渠道星级查询</title>
    <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
</head>
<body>
<!-- 容器开始 -->
<div class="container">
    <div class="row">
        <div class="row">
            <form class="form-horizontal">
                <div class="row">
                    <div class="control-group span10">
                        <label class="control-label">归属组织：</label>
                        <div class="controls">
                            <select class="easyui-combobox"
                                    data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
                                    id="district_id" disabled="disabled">
                            </select>
                        </div>
                    </div>
                    <div class="control-group span10">
                        <label class="control-label">代理商等级:  </label>
                        <div class="controls">
                            <select class="easyui-combobox" name="agentLevel"
                                    data-options="required:true,panelHeight:'auto',editable:false,valueField:'id',textField:'text'"
                                    id="agentLevel"  disabled="disabled"></select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="control-group span10">
                        <label class="control-label">代理商简称：</label>
                        <div class="controls">
                            <input id="cc" style="width: 150px;"/>
                        </div>
                        <div id="sp">
                            <div style="margin: 2px;">
                                        <span> <input id="key" class="easyui-validatebox" style="width: 90px;"/>
                                        </span> <span> <a id="treeQueryBtn" href="#" class="easyui-linkbutton"
                                                          data-options="iconCls:'icon-search'"></a>
                                        </span>
                            </div>
                            <div style="margin: 2px;">
                                <ul id="tt"></ul>
                            </div>
                        </div>
                    </div>
                    <div class="control-group span10" align="center">
                        <label class="control-label">考核月：<span style="color:red;">*</span></label>
                        <div class="controls">
                            <select class="easyui-combobox"data-options="panelHeight:'auto',required:false,editable:false,valueField:'id',textField:'text'"name="fullyear" id="fullyear"></select>年
                            <select class="easyui-combobox" id="fullmonth" data-options="panelHeight:'auto', required:false,editable:false,valueField:'id',textField:'text'"></select>月
                        </div>
                    </div>
                </div>
                <div class="row span24">
                    <div class="row offset11">
                        <div class="controls">
                            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'"
                               id="queryBtn">搜索</a>
                        </div>
                        <div class="controls">
                            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-reload'"
                               id="resetBtn">重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- operate 开始 -->
    <div id="tb">
        <div>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"
               id="export">导出</a>
        </div>
    </div>
    <!-- operate 结束 -->

    <!-- 数据显示开始 -->
    <div class="row span24">
        <table id="detaNodeList" title="查询结果"
               data-options="pageSize:10,height:350,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
            <thead>
            <tr>
                <th data-options="field:'ck',checkbox:true"></th>
                <th data-options="field:'operatorsName',align:'center',width:140">渠道名称</th>
                <th data-options="field:'opMonth',align:'center',width:140">考核月</th>
                <th data-options="field:'operatorsStars',align:'center',width:200">星级达标情况</th>
                <th data-options="field:'totalSize',align:'center',width:200">触达客户规模数</th>
                <th data-options="field:'channelStarSumFee',align:'center',width:200">渠道星级补贴核算额度</th>
            </tr>
            </thead>
        </table>
    </div>
    <!-- 数据显示结束 -->
</div>
<!-- 容器结束 -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script>
    BUI.use('module/report/assess/channelSocialStarQuery', function (channelSocialStarQuery) {
        new channelSocialStarQuery();
    });
</script>
</html>