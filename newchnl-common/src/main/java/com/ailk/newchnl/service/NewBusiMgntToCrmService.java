/*
 * $Id: NewBusiMgntToCrmService.java,v 1.20 2015/05/13 03:27:47 fuqiang Exp $
 *
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.service;

import java.util.List;
import java.util.Map;


import com.ailk.newchnl.entity.AuthorizationCardInfo;
import com.ailk.newchnl.entity.ChannelNodeInfo;
import com.ailk.newchnl.entity.QueuingInfo;
import com.ailk.newchnl.entity.crm.ChannelJson;
import net.sf.json.JSONObject;


/**
 * <AUTHOR>
 * @version $Id: NewBusiMgntToCrmService.java,v 1.20 2015/05/13 03:27:47 fuqiang Exp $
 * Created on 2014年7月31日 下午5:13:29
 */
public interface NewBusiMgntToCrmService {
    public Map<String, Object> getFlag(String param) throws Exception;

    public Map<String, Object> check_manager(String param) throws Exception;

    public Map<String, Object> validate_agentBillId(String param) throws Exception;

    public Map<String, Object> get_agentInfo(String param) throws Exception;

    public Map<String, Object> get_agentIdByOrgId(String param) throws Exception;

    public Map<String, Object> get_AgentIdByBillId(String param) throws Exception;

    public Map<String, Object> get_isResInactivePhone(String param) throws Exception;

    public Map<String, Object> update_phoneActiveStatus(String param) throws Exception;

    public Map<String, Object> update_phoneActiveStatusNew(String param) throws Exception;

    public Map<String, Object> get_policyInfo(String param) throws Exception;

    public Map<String, Object> commit_QDOnSMSOrder(String param) throws Exception;

    public Map<String, Object> get_whitecardBelongAgent(String param) throws Exception;

    public Map<String, Object> get_channelParentEntityById(String param) throws Exception;

    public Map<String, Object> check_white_card_match(String param) throws Exception;

    public Map<String, Object> operate_chnlAgentActiveNumNotify(String param) throws Exception;

    public List<JSONObject> get_channelAgentInfoDtl(String param) throws Exception;

    public Map<String, Object> get_chnlPhoneTime(String param) throws Exception;

    public Map<String, Object> get_resChannelInfo(String param) throws Exception;

    public Map<String, Object> get_ChnlNodeNameByNodeId(String param) throws Exception;

    public Map<String, Object> get_ChnlNodeIdByBillIdOnSMSOrder(String param) throws Exception;

    public Map<String, Object> get_chnlAgentBusiData(String param) throws Exception;

    public Map<String, Object> sms_nodeAuthYearCheck(String param) throws Exception;

    public Map<String, Object> get_channelInfoBySvrNum(String param) throws Exception;

    public Map<String, Object> get_singleAgentAward(String param) throws Exception;

    public List<JSONObject> get_RewardPaymentSchedule(String param) throws Exception;

    public List<JSONObject> getOrderDelivery(String param) throws Exception;

    public Map<String, Object> get_singleAgentAwardExt(String param) throws Exception;

    public Map<String, Object> get_channelInfoByMobileNum(String param) throws Exception;

    public Map<String, Object> get_agentLimit(String param) throws Exception;

    public Map<String, Object> get_entityIdByOrgIdOrMobile(String param) throws Exception;

    public Map<String, Object> getEntityInfoByOrgIdOrMonile(String param) throws Exception;

    public Map<String, Object> getAgentAwardByBillId(String param) throws Exception;

    public Map<String, Object> select_ChannelEntityAward(String param) throws Exception;

    /***
     * 号池分级选择需求1203
     */
    public Map<String, Object> getAgentPhoneLevelSchedule(String param) throws Exception;

    public List<JSONObject> get_channelParentTreeView(String param) throws Exception;

    /***
     * <AUTHOR>
     * 根据空选母卡获取对应的orgId
     * @param      param
     *             空选母卡
     */
    public Map<String, Object> getAgentChoosephoneAirBillId(String param);

    public Map<String, Object> queryNodeInfo(String param) throws Exception;

    public Map<String, Object> validateResCode(String param) throws Exception;

    /***
     * 社会渠道授权网点二维码信息查询需求
     * 二维码识别，根据授权编码查询对应接口
     */
    public Map<String, Object> getChannelNodeUnifyCode(String param);

    public List<JSONObject> select_channelEntityBasicInfo(String param) throws Exception;

    /***
     * CRM调渠道接口“获取代理商信息”【接口编码：PT-SH-FS-OI3511】，因前期梳理遗漏，目前需配合改造。  05-12
     */
    public List<JSONObject> get_agentByDistrictId(String param) throws Exception;

    public Map<String, Object> sms_nodeAuthCheck(String param) throws Exception;

    public Map<String, Object> updateChannelNodeUnifyCode(String param) throws Exception;

    /***
     * 根据crm的orgId查询对应渠道实体对应关系接口
     */
    public Map<String, Object> getChannelAgentTypeInfoOrgId(String param) throws Exception;

    /**
     * 新开业、搬迁开业、搬迁停业、营业厅关闭校验渠道网点名称是否存在
     *
     * @param param
     * @return
     * @throws Exception
     */
    public Map<String, Object> getChannelEntityNode(String param) throws Exception;

    /**
     * 提供接口，保存协同平台同步过来数据
     */
    public Map<String, Object> operaTypeChannelNodeSynerGy(String param) throws Exception;

    /**
     * 4a/crm全量查询有效数据
     *
     * @param param
     * @return
     * @throws Exception
     */
    public List<JSONObject> getChannelNodeSynerGy(String param) throws Exception;

    /**
     * 信息-2016-4767 无需使用密钥狗即可登陆移动单店问题修复
     * 入参：操作人员工号（code）
     * 出参：mac地址，如果没有找到就返回空
     *
     * @param param
     * @return
     * @throws Exception
     */
    List<Map<String, Object>> checkZydPhone(String param) throws Exception;


    /**
     * 市场-2016-9019 二维码需求处理及查询优化-代理商管理
     * 根据orgId查询网店类型
     *
     * @param param
     * @return
     * @throws Exception
     */
    public Map<String, Object> getNodeKind(String param) throws Exception;

    /**
     * 根据代理商手机号，查询网点名称，网点渠道类型，网点渠道归属
     *
     * @param param
     * @return
     * @throws Exception
     */
    public List<ChannelNodeInfo> get_channelNodeInfoBySvrNum(String param) throws Exception;


    /**
     * 信息-2017-5112 YWZC000210382-合作渠道部业务用卡渠道同步物资营销系统日对账机制需求-代理商管理
     *
     * @param param
     * @return
     * @throws Exception
     */
    public Map<String, Object> checkSellResourceSyn(String param) throws Exception;


    /**
     * 公安反诈骗查询，根据orgId查询对应的网点信息
     */
    public Map<String, Object> getChannelNode(String param);

    public Map<String, Object> getServicePhone1(String param);

    /**
     * 同步渠道新系统添加网点到老系统
     */
    public Map<String, Object> addChannelNode(String param);

    /**
     * 查询营业厅基本信息（排队机）
     */
    public List<Map<String, Object>> getChannelEntityInfo(String param) throws Exception;

    public List<Map<String, Object>> getCardInfoList(String param) throws Exception;

    public Map<String, Object> getNodeLevelAndLatitudes(String param) throws Exception;

    public Map<String, Object> getAgentInfoByOrgId(String param) throws Exception;

    public String getAgentInfoByOrgIdEsb(String param) throws Exception;

    public Map<String, String> queryPointsByAgentName(String param) throws Exception;

    public String getPartion(Long pointsSysId);

    /**
     * 获取取号模块信息
     */
    public Map<String, Object> getQueuingInfoList(String param)throws Exception;

    //通过网点名称来查取号对接系统，取号链接，网点名称
    public Map<String, Object> getQueuingInfoListByName(String param)throws Exception;

    //通过接口H5录入信息报文传过来落表
    public  Map<String, Object>setAgentApplicationInfo(String param)throws  Exception;

    //通过接口H5录入图片信息通过订单编号传过来落表
    public  Map<String, Object>setAgentApplicationFile(String param)throws  Exception;

    //通过订单编号查询合作方信息表数据
    public  Map<String, Object>getAgentApplicationInfo(String param)throws  Exception;

    //通过订单编号查询合作方信息表文件数据
    public  Map<String, Object>getAgentApplicationFile(String param)throws  Exception;

    //同步网点营业厅时间 营业状态行政区等字段给H5
    public Map<String, Object>getChannelNodeBusinessTime(String param) throws Exception;

    //通过orgId查询网点对应的网格信息
    public Map<String, Object>getChannelStreetInfo(String param) throws Exception;

    //股东认证异常回传信息入库
    public Map<String, Object>getChannelShareholderInfo(String param) throws Exception;

    //机构信息同步接口 通过orgId 同步门店电话号码、负责人、地址
    public Map<String, Object>getChannelNodeInfoByOrgId(String param) throws Exception;
    //请求响应json报文入表，切割报文方法
    public ChannelJson getChannelBusiToCrmJson(ChannelJson channelJson, String param, int index) throws Exception;

    public Map<String,Object> getUnifyCodeByNodeId(String param) throws Exception;

    public Map<String, Object> getAgentByOrgId(String param) throws Exception;

    public Map<String, Object> getOrgInfoByOrgId(String param) throws Exception;
    //根据CRM传过来的orgId和orgName查询 渠道网点对应信息
    public List<Map<String, Object>> getChannelNodeByOrgIdAndName(String param) throws Exception;
}

